import { Form } from "@douyinfe/semi-ui";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { render, screen } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { RenderItem } from "../renderItem";

// Mock 所有依赖
vi.mock("@douyinfe/semi-ui", async () => {
  const actual = await vi.importActual("@douyinfe/semi-ui");
  return {
    ...actual,
    useFormApi: () => ({
      getValue: vi.fn((field: string) => {
        const values: Record<string, any> = {
          "form.level": 1,
          "form.isUpgrade": 1,
          "form.unitCategory": 2, // 设置为2，这样contractorSearch测试会走正确的分支
        };
        return values[field] || "";
      }),
      setValue: vi.fn(),
    }),
    Form: Object.assign(
      ({ children, ...props }: any) => <form {...props}>{children}</form>,
      {
        Input: ({ label, placeholder, ...props }: any) => (
          <div data-testid="form-input">
            <label>{label}</label>
            <input placeholder={placeholder} {...props} />
          </div>
        ),
        InputNumber: ({ label, placeholder, ...props }: any) => (
          <div data-testid="form-input-number">
            <label>{label}</label>
            <input type="number" placeholder={placeholder} {...props} />
          </div>
        ),
        Select: Object.assign(
          ({ label, children, ...props }: any) => (
            <div data-testid="form-select">
              <label>{label}</label>
              <select {...props}>{children}</select>
            </div>
          ),
          {
            Option: ({ children, ...props }: any) => (
              <option {...props}>{children}</option>
            ),
          }
        ),
        RadioGroup: ({ label, children, ...props }: any) => (
          <div data-testid="form-radio-group">
            <label>{label}</label>
            <div {...props}>{children}</div>
          </div>
        ),
        Radio: ({ children, ...props }: any) => (
          <div data-testid="form-radio" {...props}>
            {children}
          </div>
        ),
        CheckboxGroup: ({ label, children, ...props }: any) => (
          <div data-testid="form-checkbox-group">
            <label>{label}</label>
            <div {...props}>{children}</div>
          </div>
        ),
        Checkbox: ({ children, ...props }: any) => (
          <div data-testid="form-checkbox" {...props}>
            {children}
          </div>
        ),
        DatePicker: ({ label, ...props }: any) => (
          <div data-testid="form-date-picker">
            <label>{label}</label>
            <input type="date" {...props} />
          </div>
        ),
        TimePicker: ({ label, ...props }: any) => (
          <div data-testid="form-time-picker">
            <label>{label}</label>
            <input type="time" {...props} />
          </div>
        ),
        TextArea: ({ label, placeholder, ...props }: any) => (
          <div data-testid="form-textarea">
            <label>{label}</label>
            <textarea placeholder={placeholder} {...props} />
          </div>
        ),
        Switch: ({ label, ...props }: any) => (
          <div data-testid="form-switch">
            <label>{label}</label>
            <input type="checkbox" {...props} />
          </div>
        ),
        Upload: ({ label, ...props }: any) => (
          <div data-testid="form-upload">
            <label>{label}</label>
            <input type="file" {...props} />
          </div>
        ),
        TagInput: ({ label, ...props }: any) => (
          <div data-testid="form-tag-input">
            <label>{label}</label>
            <input type="text" {...props} />
          </div>
        ),
      }
    ),
    Tooltip: ({ children, content }: any) => (
      <div data-testid="tooltip" title={content}>
        {children}
      </div>
    ),
  };
});

vi.mock("@tanstack/react-query", () => ({
  useQuery: () => ({
    data: {
      data: {
        results: [
          { id: 1, name: "风险措施1" },
          { id: 2, name: "风险措施2" },
        ],
      },
    },
  }),
  QueryClient: vi.fn(),
  QueryClientProvider: ({ children }: any) => children,
}));

vi.mock("atoms", () => ({
  certificateSelectAtom: vi.fn(),
  mapPickerAtom: vi.fn(),
}));

vi.mock("components", () => ({
  AreaSearch: ({ label }: any) => <div data-testid="area-search">{label}</div>,
  ContractorSearch: ({ label }: any) => (
    <div data-testid="contractor-search">{label}</div>
  ),
  DepartmentPicker: ({ label }: any) => (
    <div data-testid="department-picker">{label}</div>
  ),
  EmployeePicker: ({ label }: any) => (
    <div data-testid="employee-picker">{label}</div>
  ),
  EmployeeSearch: ({ label }: any) => (
    <div data-testid="employee-search">{label}</div>
  ),
  MapPicker: ({ label }: any) => <div data-testid="map-picker">{label}</div>,
  RISK_MEASURE_ACCIDENTTYPE: ({ label }: any) => (
    <div data-testid="risk-measure">{label}</div>
  ),
  Upload: ({ label }: any) => <div data-testid="upload">{label}</div>,
}));

vi.mock("jotai", () => ({
  useAtom: () => [null, vi.fn()],
}));

vi.mock("ramda", () => ({
  find: vi.fn(),
  pick: vi.fn(),
  propEq: vi.fn(),
  remove: vi.fn(),
  isEmpty: vi.fn((value) => {
    if (value === null || value === undefined) return true;
    if (typeof value === "string") return value.trim() === "";
    if (Array.isArray(value)) return value.length === 0;
    if (typeof value === "object") return Object.keys(value).length === 0;
    return false;
  }),
}));

vi.mock("../renderTable", () => ({
  RenderTable: ({ item }: any) => (
    <div data-testid="render-table">{item?.formData?.formName || "表格"}</div>
  ),
}));

// 测试组件包装器
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient();
  return (
    <QueryClientProvider client={queryClient}>
      <Form>{children}</Form>
    </QueryClientProvider>
  );
};

describe("RenderItem 核心功能测试", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("基础组件渲染测试", () => {
    it("应该正确渲染 plainText 组件", () => {
      const item = {
        compType: "plainText",
        formData: {
          actualValue: "测试文本",
          textType: "title",
          textAlign: "center",
        },
      };

      render(
        <TestWrapper>
          <RenderItem item={item} k={1} rule={[]} />
        </TestWrapper>
      );

      expect(screen.getByText("测试文本")).toBeInTheDocument();
    });

    it("应该正确渲染 input 组件", () => {
      const item = {
        compType: "input",
        formData: {
          formName: "测试输入",
          placeHolder: "请输入",
          isReq: "required",
        },
      };

      render(
        <TestWrapper>
          <RenderItem item={item} k={1} rule={[]} />
        </TestWrapper>
      );

      expect(screen.getByText("测试输入")).toBeInTheDocument();
    });

    it("应该正确渲染 selector 组件", () => {
      const item = {
        compType: "selector",
        formData: {
          formName: "测试选择",
          candidateList: [
            { id: "1", label: "选项1" },
            { id: "2", label: "选项2" },
          ],
        },
      };

      render(
        <TestWrapper>
          <RenderItem item={item} k={1} rule={[]} />
        </TestWrapper>
      );

      expect(screen.getByText("测试选择")).toBeInTheDocument();
    });

    it("应该正确渲染 radio 组件", () => {
      const item = {
        compType: "radio",
        formData: {
          formName: "测试单选",
          options: [
            { label: "选项1", value: "1" },
            { label: "选项2", value: "2" },
          ],
        },
      };

      render(
        <TestWrapper>
          <RenderItem item={item} k={1} rule={[]} />
        </TestWrapper>
      );

      expect(screen.getByText("测试单选")).toBeInTheDocument();
    });

    it("应该正确渲染 checkbox 组件", () => {
      const item = {
        compType: "checkbox",
        formData: {
          formName: "测试多选",
          options: [
            { label: "选项1", value: "1" },
            { label: "选项2", value: "2" },
          ],
        },
      };

      render(
        <TestWrapper>
          <RenderItem item={item} k={1} rule={[]} />
        </TestWrapper>
      );

      expect(screen.getByText("测试多选")).toBeInTheDocument();
    });

    it("应该正确渲染 datePicker 组件", () => {
      const item = {
        compType: "datePicker",
        formData: {
          formName: "测试日期",
          placeHolder: "请选择日期",
        },
      };

      render(
        <TestWrapper>
          <RenderItem item={item} k={1} rule={[]} />
        </TestWrapper>
      );

      expect(screen.getByText("测试日期")).toBeInTheDocument();
    });

    it("应该正确渲染 timePicker 组件", () => {
      const item = {
        compType: "datePicker",
        formData: {
          formName: "测试时间",
          placeHolder: "请选择时间",
        },
      };

      render(
        <TestWrapper>
          <RenderItem item={item} k={1} rule={[]} />
        </TestWrapper>
      );

      expect(screen.getByText("测试时间")).toBeInTheDocument();
    });

    it("应该正确渲染 textarea 组件", () => {
      const item = {
        compType: "input",
        formData: {
          formName: "测试文本域",
          placeHolder: "请输入内容",
        },
      };

      render(
        <TestWrapper>
          <RenderItem item={item} k={1} rule={[]} />
        </TestWrapper>
      );

      expect(screen.getByText("测试文本域")).toBeInTheDocument();
    });

    it("应该正确渲染 switch 组件", () => {
      const item = {
        compType: "radio",
        formData: {
          formName: "测试开关",
          candidateList: [
            { id: "1", label: "开" },
            { id: "2", label: "关" },
          ],
        },
      };

      render(
        <TestWrapper>
          <RenderItem item={item} k={1} rule={[]} />
        </TestWrapper>
      );

      expect(screen.getByText("测试开关")).toBeInTheDocument();
    });

    it("应该正确渲染 upload 组件", () => {
      const item = {
        compType: "annexImgPicker",
        formData: {
          formName: "测试上传",
        },
      };

      render(
        <TestWrapper>
          <RenderItem item={item} k={1} rule={[]} />
        </TestWrapper>
      );

      expect(screen.getByText("测试上传")).toBeInTheDocument();
    });
  });

  describe("特殊组件渲染测试", () => {
    it("应该正确渲染 areaSearch 组件", () => {
      const item = {
        compType: "selector",
        business: "workArea",
        formData: {
          formName: "测试区域搜索",
        },
      };

      render(
        <TestWrapper>
          <RenderItem item={item} k={1} rule={[]} />
        </TestWrapper>
      );

      expect(screen.getByTestId("area-search")).toBeInTheDocument();
    });

    it("应该正确渲染 contractorSearch 组件", () => {
      const item = {
        compType: "selector",
        business: "unit",
        formData: {
          formName: "测试承包商搜索",
        },
      };

      render(
        <TestWrapper>
          <RenderItem item={item} k={1} rule={[]} />
        </TestWrapper>
      );

      expect(screen.getByTestId("contractor-search")).toBeInTheDocument();
    });

    it("应该正确渲染 employeeSearch 组件", () => {
      const item = {
        compType: "employeePicker",
        formData: {
          formName: "测试员工搜索",
        },
      };

      render(
        <TestWrapper>
          <RenderItem item={item} k={1} rule={[]} />
        </TestWrapper>
      );

      expect(screen.getByTestId("employee-search")).toBeInTheDocument();
    });

    it("应该正确渲染 mapPicker 组件", () => {
      const item = {
        compType: "mapPicker",
        formData: {
          formName: "测试地图选择",
        },
      };

      render(
        <TestWrapper>
          <RenderItem item={item} k={1} rule={[]} />
        </TestWrapper>
      );

      expect(screen.getByTestId("map-picker")).toBeInTheDocument();
    });

    it("应该正确渲染 table 组件", () => {
      const item = {
        compType: "table",
        formData: {
          formName: "测试表格",
        },
      };

      render(
        <TestWrapper>
          <RenderItem item={item} k={1} rule={[]} />
        </TestWrapper>
      );

      expect(screen.getByTestId("render-table")).toBeInTheDocument();
    });
  });

  describe("业务逻辑测试", () => {
    it("应该正确处理高处作业验证逻辑", () => {
      const item = {
        compType: "input",
        formData: {
          formName: "高度输入",
          type: "float",
        },
        business: "height",
      };

      const rule = [
        {
          highLevel: 1,
          rangeRuleList: [
            {
              operator: 1, // 小于
              pivotNumber: 10,
            },
          ],
        },
      ];

      render(
        <TestWrapper>
          <RenderItem item={item} k={1} rule={rule} isHighWork={true} />
        </TestWrapper>
      );

      expect(screen.getByText("高度输入")).toBeInTheDocument();
    });

    it("应该正确处理表单字段名称", () => {
      const item = {
        compType: "input",
        formData: {
          formName: "测试字段",
        },
        business: "testField",
      };

      render(
        <TestWrapper>
          <RenderItem item={item} k={1} rule={[]} />
        </TestWrapper>
      );

      expect(screen.getByText("测试字段")).toBeInTheDocument();
    });

    it("应该正确处理必填字段验证", () => {
      const item = {
        compType: "input",
        formData: {
          formName: "必填字段",
          isReq: "required",
        },
      };

      render(
        <TestWrapper>
          <RenderItem item={item} k={1} rule={[]} />
        </TestWrapper>
      );

      expect(screen.getByText("必填字段")).toBeInTheDocument();
    });
  });

  describe("边界条件测试", () => {
    it("应该处理空的 formData", () => {
      const item = {
        compType: "input",
        formData: {},
      };

      render(
        <TestWrapper>
          <RenderItem item={item} k={1} rule={[]} />
        </TestWrapper>
      );

      expect(screen.getByText("未命名")).toBeInTheDocument();
    });

    it("应该处理未知的组件类型", () => {
      const item = {
        compType: "unknownType",
        formData: {
          formName: "未知组件",
        },
      };

      render(
        <TestWrapper>
          <RenderItem item={item} k={1} rule={[]} />
        </TestWrapper>
      );

      // 应该渲染默认的未定义标签
      expect(screen.getByText("未定义")).toBeInTheDocument();
    });

    it("应该处理空的 rule 数组", () => {
      const item = {
        compType: "input",
        formData: {
          formName: "测试输入",
        },
      };

      render(
        <TestWrapper>
          <RenderItem item={item} k={1} rule={[]} />
        </TestWrapper>
      );

      expect(screen.getByText("测试输入")).toBeInTheDocument();
    });
  });
});
