import { beforeEach, describe, expect, it, vi } from "vitest";

// 直接导入核心函数进行测试

// 模拟依赖
vi.mock("utils/formConverter", () => ({
  convertForm: vi.fn(),
}));

// 模拟时间验证逻辑
const validateTime = (beginTime: string, endTime: string): boolean => {
  const now = new Date();
  const begin = new Date(beginTime);
  const end = new Date(endTime);

  return end > begin && end > now;
};

// 模拟数据转换逻辑
const mockConvertForm = (form: any, elements: any[] = []): any[] => {
  const results: any[] = [];

  if (!form || typeof form !== "object") {
    return results;
  }

  Object.keys(form).forEach((key) => {
    let value = form[key];

    const business = elements.find((e) => e.business === key);
    const itemId = elements.find((e) => e.itemId === key);
    const item = business || itemId;

    // 处理 employeePicker 和 selector 类型
    if (
      item &&
      (item.compType === "employeePicker" || item.compType === "selector")
    ) {
      if (Array.isArray(value)) {
        value = value.map((o) => (typeof o === "object" ? o : JSON.parse(o)));
      } else if (typeof value === "string") {
        try {
          value = [JSON.parse(value)];
        } catch (e) {
          // 保持原值
        }
      }
    }

    // 处理 annexImgPicker 和 annexFilePicker 类型
    if (
      business &&
      (business.compType === "annexImgPicker" ||
        business.compType === "annexFilePicker")
    ) {
      if (Array.isArray(value)) {
        value = value
          .map((o) => {
            if (o?.response) {
              return (o?.response?.data?.uris ?? []).map((u: any) => u);
            } else {
              const rawData = o?.url?.split("http://localhost:3000");
              return rawData?.length ? rawData[1] : null;
            }
          })
          .flat();
      }
    }

    // 处理表格类型
    if (item && item.compType === "table" && Array.isArray(value)) {
      value = value.map((row: any) => mockConvertForm(row, item.children));
    }

    if (item) {
      results.push({
        ...item,
        formData: {
          ...item.formData,
          actualValue: value,
        },
      });
    }
  });

  return results.filter((item) => item !== null && item?.itemId);
};

describe("CreateTicketPage - 核心函数测试", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("时间验证测试", () => {
        it("应该验证结束时间大于开始时间", () => {
      const beginTime = "2030-01-01T10:00:00";
      const endTime = "2030-01-01T18:00:00";
      
      expect(validateTime(beginTime, endTime)).toBe(true);
    });

    it("应该拒绝结束时间早于开始时间", () => {
      const beginTime = "2024-01-01T18:00:00";
      const endTime = "2024-01-01T10:00:00";

      expect(validateTime(beginTime, endTime)).toBe(false);
    });

    it("应该拒绝结束时间等于开始时间", () => {
      const beginTime = "2024-01-01T10:00:00";
      const endTime = "2024-01-01T10:00:00";

      expect(validateTime(beginTime, endTime)).toBe(false);
    });

    it("应该拒绝过去的结束时间", () => {
      const beginTime = "2023-01-01T10:00:00";
      const endTime = "2023-01-01T18:00:00";

      expect(validateTime(beginTime, endTime)).toBe(false);
    });
  });

  describe("数据转换测试", () => {
    it("应该处理空的表单数据", () => {
      const result = mockConvertForm(null);
      expect(result).toEqual([]);
    });

    it("应该处理空对象", () => {
      const result = mockConvertForm({});
      expect(result).toEqual([]);
    });

    it("应该处理非对象数据", () => {
      const result = mockConvertForm("invalid");
      expect(result).toEqual([]);
    });

    it("应该正确处理 selector 类型数据", () => {
      const form = {
        department: JSON.stringify({ id: 1, name: "技术部" }),
      };

      const elements = [
        {
          business: "department",
          compType: "selector",
          itemId: "department",
          formData: {},
        },
      ];

      const result = mockConvertForm(form, elements);

      expect(result).toHaveLength(1);
      expect(result[0].formData.actualValue).toEqual([
        { id: 1, name: "技术部" },
      ]);
    });

    it("应该正确处理 annexImgPicker 类型数据", () => {
      const form = {
        upload: [
          {
            response: {
              data: {
                uris: ["/uploads/image1.jpg", "/uploads/image2.jpg"],
              },
            },
          },
        ],
      };

      const elements = [
        {
          business: "upload",
          compType: "annexImgPicker",
          itemId: "upload",
          formData: {},
        },
      ];

      const result = mockConvertForm(form, elements);

      expect(result).toHaveLength(1);
      expect(result[0].formData.actualValue).toEqual([
        "/uploads/image1.jpg",
        "/uploads/image2.jpg",
      ]);
    });

    it("应该正确处理表格类型数据", () => {
      const form = {
        tableName: [
          { name: "张三", age: "25" },
          { name: "李四", age: "30" },
        ],
      };

      const elements = [
        {
          business: "tableName",
          compType: "table",
          itemId: "tableName",
          formData: {},
          children: [
            { business: "name", compType: "input", itemId: "name" },
            { business: "age", compType: "input", itemId: "age" },
          ],
        },
      ];

      const result = mockConvertForm(form, elements);

      expect(result).toHaveLength(1);
      expect(result[0].formData.actualValue).toHaveLength(2);
    });

    it("应该过滤无效的转换结果", () => {
      const form = {
        valid: "value",
        invalid: null,
      };

      const elements = [
        {
          business: "valid",
          compType: "input",
          itemId: "valid",
          formData: {},
        },
        // 没有对应的 element，应该被过滤
      ];

      const result = mockConvertForm(form, elements);

      expect(result).toHaveLength(1);
      expect(result[0].business).toBe("valid");
    });
  });

  describe("边界情况测试", () => {
    it("应该处理无效的 JSON 数据", () => {
      const form = {
        department: "invalid json",
      };

      const elements = [
        {
          business: "department",
          compType: "selector",
          itemId: "department",
          formData: {},
        },
      ];

      const result = mockConvertForm(form, elements);

      // 应该保持原值
      expect(result[0].formData.actualValue).toBe("invalid json");
    });

    it("应该处理复杂的嵌套结构", () => {
      const form = {
        nested: {
          level1: {
            level2: {
              value: "deep",
            },
          },
        },
      };

      const elements = [
        {
          business: "nested",
          compType: "object",
          itemId: "nested",
          formData: {},
        },
      ];

      const result = mockConvertForm(form, elements);

      expect(result).toHaveLength(1);
      expect(result[0].formData.actualValue).toEqual({
        level1: {
          level2: {
            value: "deep",
          },
        },
      });
    });

    it("应该处理大量数据", () => {
      const largeForm: any = {};
      const largeElements: any[] = [];

      // 创建大量测试数据
      for (let i = 0; i < 100; i++) {
        largeForm[`field${i}`] = `value${i}`;
        largeElements.push({
          business: `field${i}`,
          compType: "input",
          itemId: `field${i}`,
          formData: {},
        });
      }

      const startTime = Date.now();
      const result = mockConvertForm(largeForm, largeElements);
      const endTime = Date.now();

      expect(result).toHaveLength(100);
      expect(endTime - startTime).toBeLessThan(100); // 应该在100ms内完成
    });
  });

  describe("性能测试", () => {
    it("应该高效处理大量表单数据", () => {
      const largeForm: any = {};
      const largeElements: any[] = [];

      // 创建1000个字段
      for (let i = 0; i < 1000; i++) {
        largeForm[`field${i}`] = `value${i}`;
        largeElements.push({
          business: `field${i}`,
          compType: "input",
          itemId: `field${i}`,
          formData: {},
        });
      }

      const startTime = Date.now();
      const result = mockConvertForm(largeForm, largeElements);
      const endTime = Date.now();

      expect(result).toHaveLength(1000);
      expect(endTime - startTime).toBeLessThan(500); // 应该在500ms内完成
    });

    it("应该正确处理内存使用", () => {
      const initialMemory = process.memoryUsage().heapUsed;

      // 执行多次转换
      for (let i = 0; i < 10; i++) {
        const form = { test: `value${i}` };
        const elements = [
          {
            business: "test",
            compType: "input",
            itemId: "test",
            formData: {},
          },
        ];

        mockConvertForm(form, elements);
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;

      // 内存增长应该在合理范围内（小于1MB）
      expect(memoryIncrease).toBeLessThan(1024 * 1024);
    });
  });
});
