
> vitamin@0.0.0 test
> vitest --coverage --coverage.reportOnFailure src/pages/ticket/config/defaultFormConfig.tsx


 DEV  v3.2.4 /Users/<USER>/Workspace/vren/chogori
      Coverage enabled with v8

10:54:40 [vite] (ssr) warning: Duplicate key "areaStyle" in object literal
457|                  color: "#82ca9d",
458|                },
459|                areaStyle: {
   |                ^
460|                  // 面积图颜色
461|                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [

  Plugin: vite:esbuild
  File: /Users/<USER>/Workspace/vren/chogori/src/pages/energyManagement/ChartPageComponent.tsx
10:54:42 [vite] (ssr) Failed to load source map for /Users/<USER>/Workspace/vren/chogori/docs/dev-log/20250625-AppRefactor-Failed-detail_files/dc99c5c6506b994b53b9.js.
Error: An error occurred while trying to read the map file at dc99c5c6506b994b53b9.js.map
Error: ENOENT: no such file or directory, open '/Users/<USER>/Workspace/vren/chogori/docs/dev-log/20250625-AppRefactor-Failed-detail_files/dc99c5c6506b994b53b9.js.map'
    at async open (node:internal/fs/promises:639:25)
    at async Object.readFile (node:internal/fs/promises:1246:14)
    at async extractSourcemapFromFile (file:///Users/<USER>/Workspace/vren/chogori/node_modules/vitest/node_modules/vite/dist/node/chunks/dep-Bg4HVnP5.js:8302:65)
    at async loadAndTransform (file:///Users/<USER>/Workspace/vren/chogori/node_modules/vitest/node_modules/vite/dist/node/chunks/dep-Bg4HVnP5.js:26425:22)
10:54:42 [vite] (ssr) Failed to load source map for /Users/<USER>/Workspace/vren/chogori/docs/dev-log/20250625-AppRefactor-Failed-detail_files/array.js.
Error: An error occurred while trying to read the map file at array.js.map
Error: ENOENT: no such file or directory, open '/Users/<USER>/Workspace/vren/chogori/docs/dev-log/20250625-AppRefactor-Failed-detail_files/array.js.map'
    at async open (node:internal/fs/promises:639:25)
    at async Object.readFile (node:internal/fs/promises:1246:14)
    at async extractSourcemapFromFile (file:///Users/<USER>/Workspace/vren/chogori/node_modules/vitest/node_modules/vite/dist/node/chunks/dep-Bg4HVnP5.js:8302:65)
    at async loadAndTransform (file:///Users/<USER>/Workspace/vren/chogori/node_modules/vitest/node_modules/vite/dist/node/chunks/dep-Bg4HVnP5.js:26425:22)
10:54:42 [vite] (ssr) Failed to load source map for /Users/<USER>/Workspace/vren/chogori/docs/dev-log/20250625-AppRefactor-Failed-detail_files/93409b67c1badd09287b.js.
Error: An error occurred while trying to read the map file at 93409b67c1badd09287b.js.map
Error: ENOENT: no such file or directory, open '/Users/<USER>/Workspace/vren/chogori/docs/dev-log/20250625-AppRefactor-Failed-detail_files/93409b67c1badd09287b.js.map'
    at async open (node:internal/fs/promises:639:25)
    at async Object.readFile (node:internal/fs/promises:1246:14)
    at async extractSourcemapFromFile (file:///Users/<USER>/Workspace/vren/chogori/node_modules/vitest/node_modules/vite/dist/node/chunks/dep-Bg4HVnP5.js:8302:65)
    at async loadAndTransform (file:///Users/<USER>/Workspace/vren/chogori/node_modules/vitest/node_modules/vite/dist/node/chunks/dep-Bg4HVnP5.js:26425:22)
10:54:42 [vite] (ssr) Failed to load source map for /Users/<USER>/Workspace/vren/chogori/docs/dev-log/20250625-AppRefactor-Failed-detail_files/3TqKJBvcfQGExqhFkAzqai.min.js.
Error: An error occurred while trying to read the map file at standalone.js.map
Error: ENOENT: no such file or directory, open '/Users/<USER>/Workspace/vren/chogori/docs/dev-log/20250625-AppRefactor-Failed-detail_files/standalone.js.map'
    at async open (node:internal/fs/promises:639:25)
    at async Object.readFile (node:internal/fs/promises:1246:14)
    at async extractSourcemapFromFile (file:///Users/<USER>/Workspace/vren/chogori/node_modules/vitest/node_modules/vite/dist/node/chunks/dep-Bg4HVnP5.js:8302:65)
    at async loadAndTransform (file:///Users/<USER>/Workspace/vren/chogori/node_modules/vitest/node_modules/vite/dist/node/chunks/dep-Bg4HVnP5.js:26425:22)
No test files found. You can change the file name pattern by pressing "p"

filter: src/pages/ticket/config/defaultFormConfig.tsx
include: **/*.{test,spec}.?(c|m)[jt]s?(x)
exclude:  **/node_modules/**, **/dist/**, **/cypress/**, **/.{idea,git,cache,output,temp}/**, **/{karma,rollup,webpack,vite,vitest,jest,ava,babel,nyc,cypress,tsup,build,eslint,prettier}.config.*

 % Coverage report from v8
-------------------|---------|----------|---------|---------|-------------------
File               | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s 
-------------------|---------|----------|---------|---------|-------------------
All files          |       0 |    14.05 |   14.05 |       0 |                   
 ...d-detail_files |       0 |        0 |       0 |       0 |                   
  ...kAzqai.min.js |       0 |        0 |       0 |       0 | 1-2               
  ...badd09287b.js |       0 |        0 |       0 |       0 | 1-8               
  array.js         |       0 |        0 |       0 |       0 | 1-2               
  ...6b994b53b9.js |       0 |        0 |       0 |       0 | 1-2               
 ...mplates/entity |       0 |    16.66 |   16.66 |       0 |                   
  api.ts           |       0 |        0 |       0 |       0 | 1-48              
  atom.tsx         |       0 |        0 |       0 |       0 | 1-60              
  content.tsx      |       0 |        0 |       0 |       0 | 1-45              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-64              
  modal.tsx        |       0 |        0 |       0 |       0 | 1-195             
  page.tsx         |       0 |      100 |     100 |       0 | 5-31              
 src               |       0 |    16.66 |   16.66 |       0 |                   
  App.tsx          |       0 |      100 |     100 |       0 | 2-198             
  auth.tsx         |       0 |        0 |       0 |       0 | 1-272             
  main.tsx         |       0 |        0 |       0 |       0 | 1-21              
  queryClient.tsx  |       0 |        0 |       0 |       0 | 1-18              
  setupTests.ts    |       0 |        0 |       0 |       0 | 1-74              
  testUtils.tsx    |       0 |        0 |       0 |       0 | 1-32              
 src/api           |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1-13              
  request.tsx      |       0 |        0 |       0 |       0 | 1-239             
 src/api/alarm     |       0 |        0 |       0 |       0 |                   
  alarmStat.ts     |       0 |        0 |       0 |       0 | 1-165             
 src/api/basicInfo |       0 |        0 |       0 |       0 |                   
  announcement.ts  |       0 |        0 |       0 |       0 | 1-48              
  calendar.ts      |       0 |        0 |       0 |       0 | 1-23              
  certificate.ts   |       0 |        0 |       0 |       0 | 1-37              
  chemical.ts      |       0 |        0 |       0 |       0 | 1-48              
  contractor.ts    |       0 |        0 |       0 |       0 | 1-75              
  ...orAccident.ts |       0 |        0 |       0 |       0 | 1-48              
  ...acklistAdd.ts |       0 |        0 |       0 |       0 | 1-48              
  ...listAppeal.ts |       0 |        0 |       0 |       0 | 1-74              
  ...ertificate.ts |       0 |        0 |       0 |       0 | 1-51              
  ...ctorConfig.ts |       0 |        0 |       0 |       0 | 1-54              
  ...orEmployee.ts |       0 |        0 |       0 |       0 | 1-65              
  ...acklistAdd.ts |       0 |        0 |       0 |       0 | 1-48              
  ...listAppeal.ts |       0 |        0 |       0 |       0 | 1-85              
  ...ertificate.ts |       0 |        0 |       0 |       0 | 1-44              
  ...EntryApply.ts |       0 |        0 |       0 |       0 | 1-67              
  ...Evaluation.ts |       0 |        0 |       0 |       0 | 1-48              
  ...torProject.ts |       0 |        0 |       0 |       0 | 1-61              
  ...Resumption.ts |       0 |        0 |       0 |       0 | 1-75              
  ...rojectStop.ts |       0 |        0 |       0 |       0 | 1-48              
  ...rViolation.ts |       0 |        0 |       0 |       0 | 1-48              
  ...ousProcess.ts |       0 |        0 |       0 |       0 | 1-48              
  department.ts    |       0 |        0 |       0 |       0 | 1-39              
  dic.ts           |       0 |        0 |       0 |       0 | 1-83              
  ...ntCategory.ts |       0 |        0 |       0 |       0 | 1-48              
  ...nformation.ts |       0 |        0 |       0 |       0 | 1-48              
  employee.ts      |       0 |        0 |       0 |       0 | 1-67              
  ...ertificate.ts |       0 |        0 |       0 |       0 | 1-48              
  ...rpriseInfo.ts |       0 |        0 |       0 |       0 | 1-47              
  equipment.ts     |       0 |        0 |       0 |       0 | 1-48              
  group.ts         |       0 |        0 |       0 |       0 | 1-54              
  index.ts         |       0 |        0 |       0 |       0 | 1-47              
  interlock.ts     |       0 |        0 |       0 |       0 | 1-48              
  lawRegulation.ts |       0 |        0 |       0 |       0 | 1-48              
  loginRecord.ts   |       0 |        0 |       0 |       0 | 1-18              
  majorHazard.ts   |       0 |        0 |       0 |       0 | 1-68              
  monitor.ts       |       0 |        0 |       0 |       0 | 1-39              
  permission.ts    |       0 |        0 |       0 |       0 | 1-66              
  position.ts      |       0 |        0 |       0 |       0 | 1-67              
  ...uctionUnit.ts |       0 |        0 |       0 |       0 | 1-48              
  ...Management.ts |       0 |        0 |       0 |       0 | 1-56              
  role.ts          |       0 |        0 |       0 |       0 | 1-55              
  stat.ts          |       0 |        0 |       0 |       0 | 1-21              
  storageRecord.ts |       0 |        0 |       0 |       0 | 1-48              
  storageTank.ts   |       0 |        0 |       0 |       0 | 1-48              
  ...geTankArea.ts |       0 |        0 |       0 |       0 | 1-48              
  ...ammableGas.ts |       0 |        0 |       0 |       0 | 1-48              
  warehouse.ts     |       0 |        0 |       0 |       0 | 1-48              
  warehouseArea.ts |       0 |        0 |       0 |       0 | 1-48              
 src/api/bigScreen |       0 |        0 |       0 |       0 |                   
  AiAnalyse.tsx    |       0 |        0 |       0 |       0 | 1-28              
  ...formation.tsx |       0 |        0 |       0 |       0 | 1-23              
  ...nMechanism.ts |       0 |        0 |       0 |       0 | 1-65              
  ...entPatrol.tsx |       0 |        0 |       0 |       0 | 1-29              
  KeyTargets.ts    |       0 |        0 |       0 |       0 | 1-26              
  ...lLocation.tsx |       0 |        0 |       0 |       0 | 1-62              
  ...Management.ts |       0 |        0 |       0 |       0 | 1-19              
  header.ts        |       0 |        0 |       0 |       0 | 1-25              
  index.ts         |       0 |        0 |       0 |       0 | 1-81              
 ...porateTraining |       0 |        0 |       0 |       0 |                   
  certificate.ts   |       0 |        0 |       0 |       0 | 1-48              
  config.ts        |       0 |        0 |       0 |       0 | 1-54              
  course.ts        |       0 |        0 |       0 |       0 | 1-48              
  courseware.ts    |       0 |        0 |       0 |       0 | 1-68              
  index.ts         |       0 |        0 |       0 |       0 | 1-14              
  paper.ts         |       0 |        0 |       0 |       0 | 1-48              
  people.ts        |       0 |        0 |       0 |       0 | 1-48              
  ...ExamRecord.ts |       0 |        0 |       0 |       0 | 1-48              
  ...tudyRecord.ts |       0 |        0 |       0 |       0 | 1-48              
  plan.ts          |       0 |        0 |       0 |       0 | 1-70              
  question.ts      |       0 |        0 |       0 |       0 | 1-68              
  record.ts        |       0 |        0 |       0 |       0 | 1-48              
  stat.ts          |       0 |        0 |       0 |       0 | 1-39              
  subject.ts       |       0 |        0 |       0 |       0 | 1-48              
  teacher.ts       |       0 |        0 |       0 |       0 | 1-48              
 ...pi/doubleGuard |       0 |     3.33 |    3.33 |       0 |                   
  area.ts          |       0 |        0 |       0 |       0 | 1-68              
  awareness.ts     |       0 |        0 |       0 |       0 | 1-50              
  bbCheck.ts       |       0 |        0 |       0 |       0 | 1-65              
  bbCheckTask.ts   |       0 |        0 |       0 |       0 | 1-48              
  bbTask.ts        |       0 |        0 |       0 |       0 | 1-50              
  ccCategory.ts    |       0 |        0 |       0 |       0 | 1-48              
  ccItem.ts        |       0 |        0 |       0 |       0 | 1-48              
  ccPlan.ts        |       0 |        0 |       0 |       0 | 1-48              
  ccTask.ts        |       0 |        0 |       0 |       0 | 1-48              
  ccTaskItem.ts    |       0 |        0 |       0 |       0 | 1-87              
  checkPlan.ts     |       0 |        0 |       0 |       0 | 1-59              
  checkTask.ts     |       0 |        0 |       0 |       0 | 1-35              
  checklist.ts     |       0 |        0 |       0 |       0 | 1-38              
  danger.ts        |       0 |        0 |       0 |       0 | 1-229             
  doubleRecord.ts  |       0 |        0 |       0 |       0 | 1-21              
  emergency.ts     |       0 |        0 |       0 |       0 | 1-50              
  evaluation.ts    |       0 |        0 |       0 |       0 | 1-94              
  ...ationRange.ts |       0 |        0 |       0 |       0 | 1-44              
  event.ts         |       0 |      100 |     100 |       0 | 2-61              
  gcCheckTask.ts   |       0 |        0 |       0 |       0 | 1-64              
  ...TaskRecord.ts |       0 |        0 |       0 |       0 | 1-123             
  gcTypeConfig.ts  |       0 |        0 |       0 |       0 | 1-48              
  incentive.ts     |       0 |        0 |       0 |       0 | 1-49              
  index.ts         |       0 |        0 |       0 |       0 | 1-33              
  measure.ts       |       0 |        0 |       0 |       0 | 1-58              
  mobile.ts        |       0 |        0 |       0 |       0 | 1-49              
  obj.ts           |       0 |        0 |       0 |       0 | 1-87              
  safety.ts        |       0 |        0 |       0 |       0 | 1-50              
  snap.ts          |       0 |        0 |       0 |       0 | 1-61              
  unit.tsx         |       0 |        0 |       0 |       0 | 1-91              
 ...encyManagement |       0 |        0 |       0 |       0 |                   
  ...nformation.ts |       0 |        0 |       0 |       0 | 1-48              
  emergencyPlan.ts |       0 |        0 |       0 |       0 | 1-48              
  ...encySupply.ts |       0 |        0 |       0 |       0 | 1-48              
  emergencyTeam.ts |       0 |        0 |       0 |       0 | 1-48              
  index.ts         |       0 |        0 |       0 |       0 | 1-4               
 ...rgy_management |       0 |        0 |       0 |       0 |                   
  xunqiao.tsx      |       0 |        0 |       0 |       0 | 1-272             
 ...ent_protection |       0 |        0 |       0 |       0 |                   
  xunqiao.ts       |       0 |        0 |       0 |       0 | 1-45              
 ...mentManagement |       0 |        0 |       0 |       0 |                   
  detection.ts     |       0 |        0 |       0 |       0 | 1-48              
  equipment.ts     |       0 |        0 |       0 |       0 | 1-63              
  ...ntCategory.ts |       0 |        0 |       0 |       0 | 1-48              
  index.ts         |       0 |        0 |       0 |       0 | 1-8               
  maintenance.ts   |       0 |        0 |       0 |       0 | 1-48              
  repair.ts        |       0 |        0 |       0 |       0 | 1-48              
  resume.ts        |       0 |        0 |       0 |       0 | 1-48              
  scrap.ts         |       0 |        0 |       0 |       0 | 1-48              
  stop.ts          |       0 |        0 |       0 |       0 | 1-48              
 ...pi/fireFighter |       0 |        0 |       0 |       0 |                   
  xunqiao.ts       |       0 |        0 |       0 |       0 | 1-45              
 ...gentInspection |       0 |        0 |       0 |       0 |                   
  index.ts         |       0 |        0 |       0 |       0 | 1-8               
  ...ctionIndex.ts |       0 |        0 |       0 |       0 | 1-38              
  ...ectionPath.ts |       0 |        0 |       0 |       0 | 1-70              
  ...ctionPlace.ts |       0 |        0 |       0 |       0 | 1-73              
  ...ectionPlan.ts |       0 |        0 |       0 |       0 | 1-48              
  ...onStandard.ts |       0 |        0 |       0 |       0 | 1-48              
  ...ectionTask.ts |       0 |        0 |       0 |       0 | 1-53              
  ...TaskRecord.ts |       0 |        0 |       0 |       0 | 1-51              
  ...TaskRecord.ts |       0 |        0 |       0 |       0 | 1-58              
 ...pi/majorHazard |       0 |        0 |       0 |       0 |                   
  alarm.ts         |       0 |        0 |       0 |       0 | 1-65              
  ...ayCategory.ts |       0 |        0 |       0 |       0 | 1-48              
  index.ts         |       0 |        0 |       0 |       0 | 1-7               
  majorStat.ts     |       0 |        0 |       0 |       0 | 1-55              
  monitorUnit.ts   |       0 |        0 |       0 |       0 | 1-48              
  ...onnelAlarm.ts |       0 |        0 |       0 |       0 | 1-59              
  sensor.ts        |       0 |        0 |       0 |       0 | 1-81              
  sensorAlarm.ts   |       0 |        0 |       0 |       0 | 1-62              
 ...sonnelLocation |       0 |        0 |       0 |       0 |                   
  index.ts         |       0 |        0 |       0 |       0 | 1                 
  url.ts           |       0 |        0 |       0 |       0 | 1-50              
 ...pi/specialWork |       0 |        0 |       0 |       0 |                   
  ...ssTemplate.ts |       0 |        0 |       0 |       0 | 1-13              
  codeConfig.ts    |       0 |        0 |       0 |       0 | 1-39              
  config.ts        |       0 |        0 |       0 |       0 | 1-45              
  gasInterval.ts   |       0 |        0 |       0 |       0 | 1-48              
  gasStandard.ts   |       0 |        0 |       0 |       0 | 1-39              
  index.ts         |       0 |        0 |       0 |       0 | 1-15              
  ...ppointment.ts |       0 |        0 |       0 |       0 | 1-77              
  jobCategory.ts   |       0 |        0 |       0 |       0 | 1-45              
  jobSlice.ts      |       0 |        0 |       0 |       0 | 1-122             
  ...ceInterval.ts |       0 |        0 |       0 |       0 | 1-48              
  jsTemplate.ts    |       0 |        0 |       0 |       0 | 1-71              
  ...ssTemplate.ts |       0 |        0 |       0 |       0 | 1-14              
  riskMeasure.ts   |       0 |        0 |       0 |       0 | 1-39              
  ...tyAnalysis.ts |       0 |        0 |       0 |       0 | 1-39              
  ...Disclosure.ts |       0 |        0 |       0 |       0 | 1-39              
  safetyMeasure.ts |       0 |        0 |       0 |       0 | 1-39              
  stat.ts          |       0 |        0 |       0 |       0 | 1-28              
 src/api/system    |       0 |        0 |       0 |       0 |                   
  ...PushConfig.ts |       0 |        0 |       0 |       0 | 1-48              
  captcha.ts       |       0 |        0 |       0 |       0 | 1-11              
  hidden.ts        |       0 |        0 |       0 |       0 | 1-71              
  index.ts         |       0 |        0 |       0 |       0 | 1-5               
  ...PushConfig.ts |       0 |        0 |       0 |       0 | 1-48              
  omConfig.ts      |       0 |        0 |       0 |       0 | 1-29              
 src/api/user      |       0 |        0 |       0 |       0 |                   
  ...e_training.ts |       0 |        0 |       0 |       0 | 1-126             
  index.ts         |       0 |        0 |       0 |       0 | 1-3               
  message.ts       |       0 |        0 |       0 |       0 | 1-90              
  system.ts        |       0 |        0 |       0 |       0 | 1-93              
 ...isionDashboard |       0 |        0 |       0 |       0 |                   
  index.ts         |       0 |        0 |       0 |       0 | 1                 
  style.ts         |       0 |        0 |       0 |       0 | 1-51              
 src/atoms         |       0 |        0 |       0 |       0 |                   
  global.ts        |       0 |        0 |       0 |       0 | 1-86              
  index.ts         |       0 |        0 |       0 |       0 | 1-12              
 ...toms/basicInfo |       0 |     4.16 |    4.16 |       0 |                   
  actionRecord.tsx |       0 |        0 |       0 |       0 | 1-96              
  announcement.tsx |       0 |        0 |       0 |       0 | 1-106             
  certificate.tsx  |       0 |        0 |       0 |       0 | 1-215             
  chemical.tsx     |       0 |        0 |       0 |       0 | 1-123             
  contractor.tsx   |       0 |        0 |       0 |       0 | 1-113             
  ...rAccident.tsx |       0 |        0 |       0 |       0 | 1-90              
  ...cklistAdd.tsx |       0 |        0 |       0 |       0 | 1-112             
  ...istAppeal.tsx |       0 |        0 |       0 |       0 | 1-219             
  ...rtificate.tsx |       0 |        0 |       0 |       0 | 1-138             
  ...torConfig.tsx |       0 |        0 |       0 |       0 | 1-60              
  ...rEmployee.tsx |       0 |        0 |       0 |       0 | 1-202             
  ...cklistAdd.tsx |       0 |        0 |       0 |       0 | 1-125             
  ...istAppeal.tsx |       0 |        0 |       0 |       0 | 1-234             
  ...rtificate.tsx |       0 |        0 |       0 |       0 | 1-159             
  ...ntryApply.tsx |       0 |        0 |       0 |       0 | 1-288             
  ...valuation.tsx |       0 |        0 |       0 |       0 | 1-141             
  ...orProject.tsx |       0 |        0 |       0 |       0 | 1-135             
  ...esumption.tsx |       0 |        0 |       0 |       0 | 1-269             
  ...ojectStop.tsx |       0 |        0 |       0 |       0 | 1-123             
  ...Violation.tsx |       0 |        0 |       0 |       0 | 1-129             
  ...usProcess.tsx |       0 |        0 |       0 |       0 | 1-70              
  department.ts    |       0 |        0 |       0 |       0 | 1-36              
  dic.tsx          |       0 |        0 |       0 |       0 | 1-71              
  dicValue.tsx     |       0 |        0 |       0 |       0 | 1-42              
  ...tCategory.tsx |       0 |        0 |       0 |       0 | 1-79              
  ...formation.tsx |       0 |        0 |       0 |       0 | 1-179             
  employee.tsx     |       0 |        0 |       0 |       0 | 1-253             
  ...rtificate.tsx |       0 |        0 |       0 |       0 | 1-118             
  ...priseInfo.tsx |       0 |      100 |     100 |       0 | 3-42              
  equipment.tsx    |       0 |        0 |       0 |       0 | 1-185             
  group.tsx        |       0 |        0 |       0 |       0 | 1-75              
  index.ts         |       0 |        0 |       0 |       0 | 1-47              
  interlock.tsx    |       0 |        0 |       0 |       0 | 1-103             
  ...egulation.tsx |       0 |        0 |       0 |       0 | 1-157             
  loginRecord.tsx  |       0 |        0 |       0 |       0 | 1-156             
  majorHazard.tsx  |       0 |        0 |       0 |       0 | 1-132             
  message.tsx      |       0 |        0 |       0 |       0 | 1-166             
  monitor.tsx      |       0 |        0 |       0 |       0 | 1-105             
  position.tsx     |       0 |      100 |     100 |       0 | 2-39              
  ...ctionUnit.tsx |       0 |        0 |       0 |       0 | 1-98              
  ...anagement.tsx |       0 |        0 |       0 |       0 | 1-171             
  role.tsx         |       0 |        0 |       0 |       0 | 1-52              
  ...ageRecord.tsx |       0 |        0 |       0 |       0 | 1-103             
  storageTank.tsx  |       0 |        0 |       0 |       0 | 1-117             
  ...eTankArea.tsx |       0 |        0 |       0 |       0 | 1-81              
  ...mmableGas.tsx |       0 |        0 |       0 |       0 | 1-61              
  warehouse.tsx    |       0 |        0 |       0 |       0 | 1-129             
  ...houseArea.tsx |       0 |        0 |       0 |       0 | 1-81              
 ...porateTraining |       0 |        0 |       0 |       0 |                   
  certificate.tsx  |       0 |        0 |       0 |       0 | 1-105             
  config.tsx       |       0 |        0 |       0 |       0 | 1-56              
  course.tsx       |       0 |        0 |       0 |       0 | 1-145             
  courseware.tsx   |       0 |        0 |       0 |       0 | 1-173             
  index.ts         |       0 |        0 |       0 |       0 | 1-13              
  my.tsx           |       0 |        0 |       0 |       0 | 1-24              
  myExamRecord.tsx |       0 |        0 |       0 |       0 | 1-153             
  ...udyRecord.tsx |       0 |        0 |       0 |       0 | 1-140             
  paper.tsx        |       0 |        0 |       0 |       0 | 1-115             
  people.tsx       |       0 |        0 |       0 |       0 | 1-133             
  ...xamRecord.tsx |       0 |        0 |       0 |       0 | 1-187             
  ...udyRecord.tsx |       0 |        0 |       0 |       0 | 1-176             
  plan.tsx         |       0 |        0 |       0 |       0 | 1-226             
  question.tsx     |       0 |        0 |       0 |       0 | 1-161             
  record.tsx       |       0 |        0 |       0 |       0 | 1-205             
  subject.tsx      |       0 |        0 |       0 |       0 | 1-69              
  teacher.tsx      |       0 |        0 |       0 |       0 | 1-129             
 ...ms/doubleGuard |       0 |     2.94 |    2.94 |       0 |                   
  allocation.tsx   |       0 |        0 |       0 |       0 | 1-418             
  area.tsx         |       0 |        0 |       0 |       0 | 1-236             
  awareness.tsx    |       0 |        0 |       0 |       0 | 1-79              
  bbCheck.tsx      |       0 |        0 |       0 |       0 | 1-191             
  bbCheckTask.tsx  |       0 |        0 |       0 |       0 | 1-273             
  bbRecord.tsx     |       0 |        0 |       0 |       0 | 1-326             
  bbTask.tsx       |       0 |        0 |       0 |       0 | 1-179             
  ccCategory.tsx   |       0 |        0 |       0 |       0 | 1-72              
  ccItem.tsx       |       0 |        0 |       0 |       0 | 1-90              
  ccPlan.tsx       |       0 |        0 |       0 |       0 | 1-144             
  ccTask.tsx       |       0 |        0 |       0 |       0 | 1-205             
  ccTaskItem.tsx   |       0 |        0 |       0 |       0 | 1-120             
  checkRecord.tsx  |       0 |        0 |       0 |       0 | 1-355             
  checkTask.tsx    |       0 |        0 |       0 |       0 | 1-360             
  checklist.tsx    |       0 |        0 |       0 |       0 | 1-166             
  danger.tsx       |       0 |        0 |       0 |       0 | 1-596             
  emergency.tsx    |       0 |        0 |       0 |       0 | 1-124             
  evaluation.tsx   |       0 |        0 |       0 |       0 | 1-25              
  event.tsx        |       0 |        0 |       0 |       0 | 1-220             
  gcCheckTask.tsx  |       0 |        0 |       0 |       0 | 1-172             
  ...askRecord.tsx |       0 |        0 |       0 |       0 | 1-168             
  ...ecordItem.tsx |       0 |        0 |       0 |       0 | 1-185             
  gcTypeConfig.tsx |       0 |        0 |       0 |       0 | 1-112             
  history.tsx      |       0 |        0 |       0 |       0 | 1-144             
  ...ification.tsx |       0 |        0 |       0 |       0 | 1-136             
  incentive.tsx    |       0 |        0 |       0 |       0 | 1-95              
  index.tsx        |       0 |        0 |       0 |       0 | 1-39              
  measure.tsx      |       0 |        0 |       0 |       0 | 1-207             
  mobile.tsx       |       0 |        0 |       0 |       0 | 1-124             
  obj.tsx          |       0 |        0 |       0 |       0 | 1-214             
  range.tsx        |       0 |      100 |     100 |       0 | 4-15              
  safety.tsx       |       0 |        0 |       0 |       0 | 1-107             
  snap.tsx         |       0 |        0 |       0 |       0 | 1-263             
  unit.tsx         |       0 |        0 |       0 |       0 | 1-229             
 ...encyManagement |       0 |        0 |       0 |       0 |                   
  ...formation.tsx |       0 |        0 |       0 |       0 | 1-82              
  ...gencyPlan.tsx |       0 |        0 |       0 |       0 | 1-119             
  ...ncySupply.tsx |       0 |        0 |       0 |       0 | 1-101             
  ...gencyTeam.tsx |       0 |        0 |       0 |       0 | 1-91              
  index.ts         |       0 |        0 |       0 |       0 | 1-4               
 ...mentManagement |       0 |        0 |       0 |       0 |                   
  detection.tsx    |       0 |        0 |       0 |       0 | 1-160             
  equipment.tsx    |       0 |        0 |       0 |       0 | 1-341             
  ...tCategory.tsx |       0 |        0 |       0 |       0 | 1-73              
  index.ts         |       0 |        0 |       0 |       0 | 1-8               
  maintenance.tsx  |       0 |        0 |       0 |       0 | 1-161             
  repair.tsx       |       0 |        0 |       0 |       0 | 1-178             
  resume.tsx       |       0 |        0 |       0 |       0 | 1-136             
  scrap.tsx        |       0 |        0 |       0 |       0 | 1-136             
  stop.tsx         |       0 |        0 |       0 |       0 | 1-136             
 ...gentInspection |       0 |        0 |       0 |       0 |                   
  index.ts         |       0 |        0 |       0 |       0 | 1-7               
  ...ctionPath.tsx |       0 |        0 |       0 |       0 | 1-97              
  ...tionPlace.tsx |       0 |        0 |       0 |       0 | 1-138             
  ...ctionPlan.tsx |       0 |        0 |       0 |       0 | 1-116             
  ...nStandard.tsx |       0 |        0 |       0 |       0 | 1-254             
  ...ctionTask.tsx |       0 |        0 |       0 |       0 | 1-180             
  ...askRecord.tsx |       0 |        0 |       0 |       0 | 1-201             
  ...askRecord.tsx |       0 |        0 |       0 |       0 | 1-121             
 ...ms/majorHazard |       0 |        0 |       0 |       0 |                   
  alarm.tsx        |       0 |        0 |       0 |       0 | 1-165             
  ...yCategory.tsx |       0 |        0 |       0 |       0 | 1-67              
  index.ts         |       0 |        0 |       0 |       0 | 1-6               
  monitorUnit.tsx  |       0 |        0 |       0 |       0 | 1-114             
  ...nnelAlarm.tsx |       0 |        0 |       0 |       0 | 1-174             
  sensor.tsx       |       0 |        0 |       0 |       0 | 1-402             
  sensorAlarm.tsx  |       0 |        0 |       0 |       0 | 1-250             
 ...sonnelLocation |       0 |        0 |       0 |       0 |                   
  index.ts         |       0 |        0 |       0 |       0 | 1                 
  url.tsx          |       0 |        0 |       0 |       0 | 1-45              
 src/atoms/search  |       0 |        0 |       0 |       0 |                   
  ...entSearch.tsx |       0 |        0 |       0 |       0 | 1-8               
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
 ...ms/specialWork |       0 |        0 |       0 |       0 |                   
  codeConfig.tsx   |       0 |        0 |       0 |       0 | 1-73              
  config.tsx       |       0 |        0 |       0 |       0 | 1-56              
  gasInterval.tsx  |       0 |        0 |       0 |       0 | 1-90              
  gasStandard.tsx  |       0 |        0 |       0 |       0 | 1-86              
  index.tsx        |       0 |        0 |       0 |       0 | 1-14              
  jobAppoiment.tsx |       0 |        0 |       0 |       0 | 1-165             
  jobCategory.tsx  |       0 |        0 |       0 |       0 | 1-118             
  jobSlice.tsx     |       0 |        0 |       0 |       0 | 1-708             
  ...eInterval.tsx |       0 |        0 |       0 |       0 | 1-98              
  jsTemplate.tsx   |       0 |        0 |       0 |       0 | 1-47              
  ...plateUser.tsx |       0 |        0 |       0 |       0 | 1-198             
  riskMeasure.tsx  |       0 |        0 |       0 |       0 | 1-90              
  ...yAnalysis.tsx |       0 |        0 |       0 |       0 | 1-163             
  ...isclosure.tsx |       0 |        0 |       0 |       0 | 1-64              
  ...tyMeasure.tsx |       0 |        0 |       0 |       0 | 1-66              
 src/atoms/system  |       0 |       25 |      25 |       0 |                   
  ...ushConfig.tsx |       0 |        0 |       0 |       0 | 1-108             
  index.ts         |       0 |        0 |       0 |       0 | 1-3               
  ...ushConfig.tsx |       0 |        0 |       0 |       0 | 1-79              
  omConfig.tsx     |       0 |      100 |     100 |       0 | 3-42              
 ...isionDashboard |       0 |        0 |       0 |       0 |                   
  index.ts         |       0 |        0 |       0 |       0 | 1                 
  style.tsx        |       0 |        0 |       0 |       0 | 1-60              
 src/components    |       0 |    22.22 |   22.22 |       0 |                   
  Draft.tsx        |       0 |        0 |       0 |       0 | 1-143             
  Head.tsx         |       0 |        0 |       0 |       0 | 1-13              
  ...tribution.tsx |       0 |      100 |     100 |       0 | 8-41              
  ...ngOrError.tsx |       0 |      100 |     100 |       0 | 6-17              
  SignaturePad.tsx |       0 |        0 |       0 |       0 | 1-114             
  index.tsx        |       0 |        0 |       0 |       0 | 1-101             
  print.tsx        |       0 |        0 |       0 |       0 | 1-49              
  tableConfig.tsx  |       0 |        0 |       0 |       0 | 1-150             
  upload.tsx       |       0 |        0 |       0 |       0 | 1-528             
 ...mponents/chart |       0 |        0 |       0 |       0 |                   
  ...etailCard.tsx |       0 |        0 |       0 |       0 | 1-196             
  PieChart.tsx     |       0 |        0 |       0 |       0 | 1-188             
  StatCard.tsx     |       0 |        0 |       0 |       0 | 1-138             
  TableChart.tsx   |       0 |        0 |       0 |       0 | 1-297             
  ...eriodCard.tsx |       0 |        0 |       0 |       0 | 1-125             
  TrendChart.tsx   |       0 |        0 |       0 |       0 | 1-232             
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  line.tsx         |       0 |        0 |       0 |       0 | 1-369             
 ...ponents/detail |       0 |    14.28 |   14.28 |       0 |                   
  ...rtToolbar.tsx |       0 |        0 |       0 |       0 | 1-149             
  ...erToolbar.tsx |       0 |        0 |       0 |       0 | 1-194             
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  ...deTabPane.tsx |       0 |        0 |       0 |       0 | 1-511             
  ...bPaneList.tsx |       0 |        0 |       0 |       0 | 1-286             
  ...taticData.tsx |       0 |        0 |       0 |       0 | 1-389             
  sideDetail.tsx   |       0 |      100 |     100 |       0 | 3-100             
 ...omponents/enum |       0 |    81.81 |   81.81 |       0 |                   
  basicInfo.tsx    |       0 |      100 |     100 |       0 | 3-1085            
  common.tsx       |       0 |        0 |       0 |       0 |                   
  ...eTraining.tsx |       0 |      100 |     100 |       0 | 3-182             
  doubleGuard.tsx  |       0 |        0 |       0 |       0 | 1-1115            
  ...anagement.tsx |       0 |      100 |     100 |       0 | 4-100             
  ...anagement.tsx |       0 |      100 |     100 |       0 | 3-63              
  index.tsx        |       0 |        0 |       0 |       0 | 1-9               
  ...nspection.tsx |       0 |      100 |     100 |       0 | 3-143             
  majorHazard.tsx  |       0 |      100 |     100 |       0 | 3-366             
  specialWork.tsx  |       0 |      100 |     100 |       0 | 3-332             
  system.tsx       |       0 |      100 |     100 |       0 | 3-712             
 ...ponents/export |       0 |    66.66 |   66.66 |       0 |                   
  ...rtFunction.ts |       0 |        0 |       0 |       0 | 1-21              
  ...tProvider.tsx |       0 |      100 |     100 |       0 | 2-100             
  exportToExcel.ts |       0 |      100 |     100 |       0 | 2-57              
  index.ts         |       0 |        0 |       0 |       0 | 1-3               
  types.ts         |       0 |        0 |       0 |       0 |                   
  useExport.ts     |       0 |      100 |     100 |       0 | 2-16              
 ...omponents/list |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
  list.tsx         |       0 |        0 |       0 |       0 | 1-642             
  myList.tsx       |       0 |        0 |       0 |       0 | 1-562             
  rightList.tsx    |       0 |        0 |       0 |       0 | 1-641             
  simpleList.tsx   |       0 |        0 |       0 |       0 | 1-109             
 ...mponents/modal |       0 |        0 |       0 |       0 |                   
  copyModal.tsx    |       0 |        0 |       0 |       0 | 1-34              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
  videoModal.tsx   |       0 |        0 |       0 |       0 | 1-60              
 ...ponents/search |       0 |     4.76 |    4.76 |       0 |                   
  areaSearch.tsx   |       0 |        0 |       0 |       0 | 1-93              
  ...ateSearch.tsx |       0 |        0 |       0 |       0 | 1-71              
  ...yeeSearch.tsx |       0 |        0 |       0 |       0 | 1-134             
  ...torSearch.tsx |       0 |        0 |       0 |       0 | 1-76              
  ...entSearch.tsx |       0 |        0 |       0 |       0 | 1-131             
  ...nitSearch.tsx |       0 |      100 |     100 |       0 | 2-66              
  dicSearch.tsx    |       0 |        0 |       0 |       0 | 1-59              
  ...yeeSearch.tsx |       0 |        0 |       0 |       0 | 1-514             
  eventSearch.tsx  |       0 |        0 |       0 |       0 | 1-88              
  groupSearch.tsx  |       0 |        0 |       0 |       0 | 1-67              
  imeiSearch.tsx   |       0 |        0 |       0 |       0 | 1-72              
  index.tsx        |       0 |        0 |       0 |       0 | 1-17              
  objectSearch.tsx |       0 |        0 |       0 |       0 | 1-82              
  ...ionSearch.tsx |       0 |        0 |       0 |       0 | 1-59              
  ...velSearch.tsx |       0 |        0 |       0 |       0 | 1-51              
  roleSearch.tsx   |       0 |        0 |       0 |       0 | 1-57              
  ...rseSearch.tsx |       0 |        0 |       0 |       0 | 1-398             
  ...perSearch.tsx |       0 |        0 |       0 |       0 | 1-400             
  ...ectSearch.tsx |       0 |        0 |       0 |       0 | 1-128             
  ...herSearch.tsx |       0 |        0 |       0 |       0 | 1-427             
  unitSearch.tsx   |       0 |        0 |       0 |       0 | 1-101             
 ...ponents/select |       0 |       10 |      10 |       0 |                   
  ...apPolygon.tsx |       0 |        0 |       0 |       0 | 1-367             
  bbWorkType.tsx   |       0 |        0 |       0 |       0 | 1-79              
  ...CycleUnit.tsx |       0 |      100 |     100 |       0 | 3-40              
  classify.tsx     |       0 |        0 |       0 |       0 | 1-88              
  index.tsx        |       0 |        0 |       0 |       0 | 1-15              
  ...orySelect.tsx |       0 |        0 |       0 |       0 | 1-48              
  ...torSelect.tsx |       0 |        0 |       0 |       0 | 1-52              
  restSelect.tsx   |       0 |        0 |       0 |       0 | 1-46              
  safetySigns.tsx  |       0 |        0 |       0 |       0 | 1-122             
  ...apPolygon.tsx |       0 |        0 |       0 |       0 | 1-155             
 .../areaMapMarker |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1-157             
 .../areaMapPicker |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1-327             
 ...tificatePicker |       0 |        0 |       0 |       0 |                   
  contentList.tsx  |       0 |        0 |       0 |       0 | 1-90              
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  insider.tsx      |       0 |        0 |       0 |       0 | 1-107             
  picker.tsx       |       0 |        0 |       0 |       0 | 1-108             
  right.tsx        |       0 |        0 |       0 |       0 | 1-139             
 ...partmentPicker |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  picker.tsx       |       0 |        0 |       0 |       0 | 1-99              
 ...employeePicker |       0 |        0 |       0 |       0 |                   
  contentList.tsx  |       0 |        0 |       0 |       0 | 1-133             
  ...ctorGroup.tsx |       0 |        0 |       0 |       0 | 1-222             
  department.tsx   |       0 |        0 |       0 |       0 | 1-131             
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  picker.tsx       |       0 |        0 |       0 |       0 | 1-283             
  workGroup.tsx    |       0 |        0 |       0 |       0 | 1-215             
 ...lect/mapPicker |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1-135             
 ...t/selectPicker |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  tmplSelect.tsx   |       0 |        0 |       0 |       0 | 1-34              
 ...omponents/side |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  renderSide.tsx   |       0 |        0 |       0 |       0 | 1-159             
 ...mponents/table |       0 |        0 |       0 |       0 |                   
  ...lectModal.tsx |       0 |        0 |       0 |       0 | 1-136             
  ...ableModal.tsx |       0 |        0 |       0 |       0 | 1-210             
  ...ckerTable.tsx |       0 |        0 |       0 |       0 | 1-295             
  iiTableModal.tsx |       0 |        0 |       0 |       0 | 1-151             
  index.tsx        |       0 |        0 |       0 |       0 | 1-5               
  ...ableModal.tsx |       0 |        0 |       0 |       0 | 1-191             
  pickerTable.tsx  |       0 |        0 |       0 |       0 | 1-218             
  ...ckerTable.tsx |       0 |        0 |       0 |       0 | 1-290             
  ...ableModal.tsx |       0 |        0 |       0 |       0 | 1-238             
  ...ableModal.tsx |       0 |        0 |       0 |       0 | 1-262             
  ...ableModal.tsx |       0 |        0 |       0 |       0 | 1-146             
  ...ckerTable.tsx |       0 |        0 |       0 |       0 | 1-213             
  ...ableModal.tsx |       0 |        0 |       0 |       0 | 1-201             
  tableModal.tsx   |       0 |        0 |       0 |       0 | 1-209             
 ...ts/tiandituMap |       0 |    66.66 |   66.66 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
  tdMap.tsx        |       0 |      100 |     100 |       0 | 2-491             
  tdPcMap.tsx      |       0 |      100 |     100 |       0 | 2-199             
 ...omponents/tool |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  uploadTmpl.tsx   |       0 |        0 |       0 |       0 | 1-146             
 ...omponents/tree |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-250             
  index.tsx        |       0 |        0 |       0 |       0 | 1-65              
  left.tsx         |       0 |        0 |       0 |       0 | 1-104             
  modal.tsx        |       0 |        0 |       0 |       0 | 1-253             
  search.tsx       |       0 |        0 |       0 |       0 | 1-94              
 src/config        |       0 |    33.33 |   33.33 |       0 |                   
  index.ts         |       0 |        0 |       0 |       0 | 1-2               
  roleKvMap.ts     |       0 |      100 |     100 |       0 | 8-17              
  system.ts        |       0 |        0 |       0 |       0 | 1-169             
 src/hooks         |       0 |     12.5 |    12.5 |       0 |                   
  ...tingHooks.tsx |       0 |        0 |       0 |       0 | 1-10              
  cesiumInit.tsx   |       0 |        0 |       0 |       0 | 1-107             
  ...archHooks.tsx |       0 |        0 |       0 |       0 | 1-106             
  index.tsx        |       0 |        0 |       0 |       0 | 1-10              
  remoteSearch.tsx |       0 |        0 |       0 |       0 | 1-10              
  useBtnHooks.tsx  |       0 |        0 |       0 |       0 | 1-53              
  ...ngFormApi.tsx |       0 |        0 |       0 |       0 | 1-81              
  ...actorList.tsx |       0 |        0 |       0 |       0 | 1-62              
  ...angerAuth.tsx |       0 |        0 |       0 |       0 | 1-71              
  ...leteHooks.tsx |       0 |        0 |       0 |       0 | 1-112             
  ...GuardList.tsx |       0 |        0 |       0 |       0 | 1-20              
  ...pmentList.tsx |       0 |        0 |       0 |       0 | 1-22              
  useMenuHooks.tsx |       0 |        0 |       0 |       0 | 1-843             
  ...hangeReset.ts |       0 |      100 |     100 |       0 | 2-27              
  ...dAppHooks.tsx |       0 |        0 |       0 |       0 | 1-60              
  ...bleConfig.tsx |       0 |      100 |     100 |       0 | 2-108             
 src/mocks         |       0 |    33.33 |   33.33 |       0 |                   
  demo.ts          |       0 |        0 |       0 |       0 |                   
  index.ts         |       0 |        0 |       0 |       0 | 1                 
  riskObject.ts    |       0 |        0 |       0 |       0 | 1-45              
 ...ks/doubleGuard |       0 |        0 |       0 |       0 |                   
  riskObject.ts    |       0 |        0 |       0 |       0 | 1-17              
 src/pages/alarm   |       0 |        0 |       0 |       0 |                   
  ...exContent.tsx |       0 |        0 |       0 |       0 | 1-42              
  ...IndexPage.tsx |       0 |        0 |       0 |       0 | 1-9               
 ...alarm/analysis |       0 |        0 |       0 |       0 |                   
  ...lysisPage.tsx |       0 |        0 |       0 |       0 | 1-29              
  ...lysisPage.tsx |       0 |        0 |       0 |       0 | 1-28              
  ...lysisPage.tsx |       0 |        0 |       0 |       0 | 1-301             
  ...lysisPage.tsx |       0 |        0 |       0 |       0 | 1-24              
  ...lysisPage.tsx |       0 |        0 |       0 |       0 | 1-82              
  ...lysisPage.tsx |       0 |        0 |       0 |       0 | 1-224             
  ...lysisPage.tsx |       0 |        0 |       0 |       0 | 1-24              
 ...arm/components |       0 |        0 |       0 |       0 |                   
  ...tatsTable.tsx |       0 |        0 |       0 |       0 | 1-187             
  ...tatsTable.tsx |       0 |        0 |       0 |       0 | 1-192             
  ...iorityPie.tsx |       0 |        0 |       0 |       0 | 1-79              
  ...rendChart.tsx |       0 |        0 |       0 |       0 | 1-48              
  BasicStats.tsx   |       0 |        0 |       0 |       0 | 1-61              
  FilterBar.tsx    |       0 |        0 |       0 |       0 | 1-279             
  ...orTypePie.tsx |       0 |        0 |       0 |       0 | 1-87              
 ...ages/basicInfo |       0 |    71.69 |   71.69 |       0 |                   
  aboutPage.tsx    |       0 |      100 |     100 |       0 | 2-10              
  ...ecordPage.tsx |       0 |        0 |       0 |       0 | 1-13              
  ...ementPage.tsx |       0 |      100 |     100 |       0 | 5-24              
  calendarPage.tsx |       0 |      100 |     100 |       0 | 2-10              
  ...icatePage.tsx |       0 |        0 |       0 |       0 | 1-15              
  chemicalPage.tsx |       0 |      100 |     100 |       0 | 5-20              
  ...identPage.tsx |       0 |      100 |     100 |       0 | 5-20              
  ...stAddPage.tsx |       0 |      100 |     100 |       0 | 5-31              
  ...ppealPage.tsx |       0 |      100 |     100 |       0 | 5-35              
  ...icatePage.tsx |       0 |      100 |     100 |       0 | 5-23              
  ...onfigPage.tsx |       0 |      100 |     100 |       0 | 5-27              
  ...stAddPage.tsx |       0 |      100 |     100 |       0 | 5-31              
  ...ppealPage.tsx |       0 |      100 |     100 |       0 | 5-35              
  ...icatePage.tsx |       0 |        0 |       0 |       0 | 1-21              
  ...loyeePage.tsx |       0 |        0 |       0 |       0 | 1-14              
  ...ApplyPage.tsx |       0 |      100 |     100 |       0 | 5-35              
  ...ationPage.tsx |       0 |      100 |     100 |       0 | 5-31              
  ...actorPage.tsx |       0 |      100 |     100 |       0 | 5-334             
  ...ojectPage.tsx |       0 |      100 |     100 |       0 | 5-22              
  ...ptionPage.tsx |       0 |      100 |     100 |       0 | 5-35              
  ...tStopPage.tsx |       0 |      100 |     100 |       0 | 5-31              
  ...ationPage.tsx |       0 |      100 |     100 |       0 | 5-31              
  ...ocessPage.tsx |       0 |      100 |     100 |       0 | 5-20              
  ...tmentPage.tsx |       0 |      100 |     100 |       0 | 5-18              
  dicPage.tsx      |       0 |        0 |       0 |       0 | 1-26              
  ...egoryPage.tsx |       0 |      100 |     100 |       0 | 5-28              
  ...ationPage.tsx |       0 |      100 |     100 |       0 | 5-38              
  employeePage.tsx |       0 |        0 |       0 |       0 | 1-16              
  ...icatePage.tsx |       0 |      100 |     100 |       0 | 5-23              
  ...IndexPage.tsx |       0 |        0 |       0 |       0 | 1-12              
  ...eInfoPage.tsx |       0 |      100 |     100 |       0 | 5-935             
  ...pmentPage.tsx |       0 |      100 |     100 |       0 | 5-64              
  groupPage.tsx    |       0 |      100 |     100 |       0 | 2-13              
  index.tsx        |       0 |        0 |       0 |       0 | 1-53              
  ...rlockPage.tsx |       0 |      100 |     100 |       0 | 5-20              
  ...ationPage.tsx |       0 |      100 |     100 |       0 | 5-20              
  ...ecordPage.tsx |       0 |        0 |       0 |       0 | 1-10              
  ...azardPage.tsx |       0 |      100 |     100 |       0 | 5-22              
  messagePage.tsx  |       0 |        0 |       0 |       0 | 1-59              
  ...IndexPage.tsx |       0 |        0 |       0 |       0 | 1-9               
  monitorPage.tsx  |       0 |        0 |       0 |       0 | 1-12              
  page.tsx         |       0 |      100 |     100 |       0 | 5-14              
  ...ssionPage.tsx |       0 |        0 |       0 |       0 | 1-9               
  positionPage.tsx |       0 |        0 |       0 |       0 | 1-13              
  ...nUnitPage.tsx |       0 |      100 |     100 |       0 | 5-20              
  ...ementPage.tsx |       0 |      100 |     100 |       0 | 5-22              
  rolePage.tsx     |       0 |        0 |       0 |       0 | 1-13              
  ...ecordPage.tsx |       0 |      100 |     100 |       0 | 5-20              
  ...kAreaPage.tsx |       0 |      100 |     100 |       0 | 5-20              
  ...eTankPage.tsx |       0 |      100 |     100 |       0 | 5-20              
  ...leGasPage.tsx |       0 |      100 |     100 |       0 | 5-20              
  ...eAreaPage.tsx |       0 |      100 |     100 |       0 | 5-20              
  ...housePage.tsx |       0 |      100 |     100 |       0 | 5-20              
 ...icInfo/content |       0 |    23.52 |   23.52 |       0 |                   
  aboutContent.tsx |       0 |        0 |       0 |       0 | 1-70              
  atom.tsx         |       0 |        0 |       0 |       0 | 1-77              
  ...arContent.tsx |       0 |        0 |       0 |       0 | 1-222             
  content.tsx      |       0 |        0 |       0 |       0 | 1-132             
  ...ntContent.tsx |       0 |        0 |       0 |       0 | 1-244             
  ...entFilter.tsx |       0 |      100 |     100 |       0 | 2-58              
  ...tmentLeft.tsx |       0 |        0 |       0 |       0 | 1-154             
  ...eeContent.tsx |       0 |        0 |       0 |       0 | 1-457             
  ...yeeFilter.tsx |       0 |        0 |       0 |       0 | 1-75              
  filter.tsx       |       0 |      100 |     100 |       0 | 2-107             
  groupContent.tsx |       0 |        0 |       0 |       0 | 1-312             
  groupFilter.tsx  |       0 |      100 |     100 |       0 | 3-53              
  index.tsx        |       0 |        0 |       0 |       0 | 1-59              
  ...rdContent.tsx |       0 |        0 |       0 |       0 | 1-157             
  ...ordFilter.tsx |       0 |        0 |       0 |       0 | 1-121             
  ...onContent.tsx |       0 |        0 |       0 |       0 | 1-325             
  ...ionFilter.tsx |       0 |      100 |     100 |       0 | 2-50              
 ...t/actionRecord |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-96              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-60              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...t/announcement |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-33              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-70              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...nt/certificate |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-442             
  contractor.tsx   |       0 |        0 |       0 |       0 | 1-306             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-117             
  index.tsx        |       0 |        0 |       0 |       0 | 1-3               
 ...ntent/chemical |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-16              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-87              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ent/contractor |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-141             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-79              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ractorAccident |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-11              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-68              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...orBlacklistAdd |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-46              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-81              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...lacklistAppeal |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-103             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-106             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...torCertificate |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-13              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-70              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ntractorConfig |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-152             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-64              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ractorEmployee |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-82              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-85              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...eeBlacklistAdd |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-46              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-87              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...lacklistAppeal |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-105             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-106             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...yeeCertificate |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-388             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-137             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ctorEntryApply |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-103             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-98              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ctorEvaluation |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-45              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-121             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...tractorProject |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-76              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-80              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...jectResumption |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-104             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-111             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...torProjectStop |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-46              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-93              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...actorViolation |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-45              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-84              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ngerousProcess |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-20              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-74              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...fo/content/dic |       0 |       40 |      40 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-335             
  filter.tsx       |       0 |      100 |     100 |       0 | 2-50              
  index.tsx        |       0 |        0 |       0 |       0 | 1-3               
  side.tsx         |       0 |        0 |       0 |       0 | 1-339             
  sideFilter.tsx   |       0 |      100 |     100 |       0 | 2-50              
 ...cumentCategory |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-43              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-62              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...entInformation |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-47              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-71              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
  left.tsx         |       0 |        0 |       0 |       0 | 1-175             
 ...iseCertificate |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-18              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-88              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...enterpriseInfo |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-18              
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
 ...priseInfoIndex |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-356             
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
 ...tent/equipment |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-24              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-137             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...tent/interlock |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-7               
  filter.tsx       |       0 |        0 |       0 |       0 | 1-62              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 .../lawRegulation |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-15              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-90              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...nt/majorHazard |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-37              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-61              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ontent/message |       0 |    33.33 |   33.33 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-302             
  filter.tsx       |       0 |      100 |     100 |       0 | 16-82             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ontent/monitor |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-319             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-63              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...t/monitorIndex |       0 |        0 |       0 |       0 |                   
  basicStat.tsx    |       0 |        0 |       0 |       0 | 1-85              
  content.tsx      |       0 |        0 |       0 |       0 | 1-28              
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  ...rdRecords.tsx |       0 |        0 |       0 |       0 | 1-172             
  ...rUnitInfo.tsx |       0 |        0 |       0 |       0 | 1-197             
  recordLeft.tsx   |       0 |        0 |       0 |       0 | 1-131             
  recordRight.tsx  |       0 |        0 |       0 |       0 | 1-159             
  sensorAlarm.tsx  |       0 |        0 |       0 |       0 | 1-71              
 ...ent/permission |       0 |       25 |      25 |       0 |                   
  appRole.tsx      |       0 |        0 |       0 |       0 | 1-516             
  content.tsx      |       0 |      100 |     100 |       0 | 2-19              
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  pcRole.tsx       |       0 |        0 |       0 |       0 | 1-344             
 ...productionUnit |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-20              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-62              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...riskManagement |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-74              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-75              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...o/content/role |       0 |    14.28 |   14.28 |       0 |                   
  addEmployee.tsx  |       0 |        0 |       0 |       0 | 1-124             
  content.tsx      |       0 |        0 |       0 |       0 | 1-340             
  employeeList.tsx |       0 |        0 |       0 |       0 | 1-172             
  filter.tsx       |       0 |      100 |     100 |       0 | 2-48              
  index.tsx        |       0 |        0 |       0 |       0 | 1-3               
  settingRole.tsx  |       0 |        0 |       0 |       0 | 1-328             
  side.tsx         |       0 |        0 |       0 |       0 | 1-63              
 .../storageRecord |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-7               
  filter.tsx       |       0 |        0 |       0 |       0 | 1-75              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...nt/storageTank |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-7               
  filter.tsx       |       0 |        0 |       0 |       0 | 1-76              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...torageTankArea |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-7               
  filter.tsx       |       0 |        0 |       0 |       0 | 1-62              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...icFlammableGas |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-7               
  filter.tsx       |       0 |        0 |       0 |       0 | 1-74              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...tent/warehouse |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-7               
  filter.tsx       |       0 |        0 |       0 |       0 | 1-75              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 .../warehouseArea |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-7               
  filter.tsx       |       0 |        0 |       0 |       0 | 1-61              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...basicInfo/head |       0 |    33.33 |   33.33 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-171             
  filter.tsx       |       0 |      100 |     100 |       0 | 2-103             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...basicInfo/mock |       0 |        0 |       0 |       0 |                   
  index.ts         |       0 |        0 |       0 |       0 |                   
 ...asicInfo/modal |       0 |        0 |       0 |       0 |                   
  ...mentModal.tsx |       0 |        0 |       0 |       0 | 1-258             
  ...cateModal.tsx |       0 |        0 |       0 |       0 | 1-210             
  ...icalModal.tsx |       0 |        0 |       0 |       0 | 1-592             
  ...dentModal.tsx |       0 |        0 |       0 |       0 | 1-274             
  ...lackModal.tsx |       0 |        0 |       0 |       0 | 1-154             
  ...tAddModal.tsx |       0 |        0 |       0 |       0 | 1-227             
  ...gateModal.tsx |       0 |        0 |       0 |       0 | 1-181             
  ...uditModal.tsx |       0 |        0 |       0 |       0 | 1-205             
  ...pealModal.tsx |       0 |        0 |       0 |       0 | 1-242             
  ...cateModal.tsx |       0 |        0 |       0 |       0 | 1-242             
  ...nfigModal.tsx |       0 |        0 |       0 |       0 | 1-195             
  ...lackModal.tsx |       0 |        0 |       0 |       0 | 1-156             
  ...tAddModal.tsx |       0 |        0 |       0 |       0 | 1-236             
  ...gateModal.tsx |       0 |        0 |       0 |       0 | 1-182             
  ...uditModal.tsx |       0 |        0 |       0 |       0 | 1-205             
  ...pealModal.tsx |       0 |        0 |       0 |       0 | 1-244             
  ...cateModal.tsx |       0 |        0 |       0 |       0 | 1-307             
  ...oyeeModal.tsx |       0 |        0 |       0 |       0 | 1-270             
  ...gateModal.tsx |       0 |        0 |       0 |       0 | 1-184             
  ...uditModal.tsx |       0 |        0 |       0 |       0 | 1-207             
  ...pplyModal.tsx |       0 |        0 |       0 |       0 | 1-276             
  ...tionModal.tsx |       0 |        0 |       0 |       0 | 1-360             
  ...ctorModal.tsx |       0 |        0 |       0 |       0 | 1-297             
  ...jectModal.tsx |       0 |        0 |       0 |       0 | 1-348             
  ...StopModal.tsx |       0 |        0 |       0 |       0 | 1-189             
  ...gateModal.tsx |       0 |        0 |       0 |       0 | 1-181             
  ...uditModal.tsx |       0 |        0 |       0 |       0 | 1-207             
  ...tionModal.tsx |       0 |        0 |       0 |       0 | 1-277             
  ...StopModal.tsx |       0 |        0 |       0 |       0 | 1-234             
  ...hareModal.tsx |       0 |        0 |       0 |       0 | 1-211             
  ...tionModal.tsx |       0 |        0 |       0 |       0 | 1-280             
  ...cessModal.tsx |       0 |        0 |       0 |       0 | 1-417             
  ...mentModal.tsx |       0 |        0 |       0 |       0 | 1-172             
  dicModal.tsx     |       0 |        0 |       0 |       0 | 1-162             
  ...alueModal.tsx |       0 |        0 |       0 |       0 | 1-169             
  ...goryModal.tsx |       0 |        0 |       0 |       0 | 1-223             
  ...tionModal.tsx |       0 |        0 |       0 |       0 | 1-293             
  ...oyeeModal.tsx |       0 |        0 |       0 |       0 | 1-317             
  ...cateModal.tsx |       0 |        0 |       0 |       0 | 1-252             
  ...InfoModal.tsx |       0 |        0 |       0 |       0 | 1-177             
  ...mentModal.tsx |       0 |        0 |       0 |       0 | 1-408             
  groupModal.tsx   |       0 |        0 |       0 |       0 | 1-211             
  index.tsx        |       0 |        0 |       0 |       0 | 1-47              
  ...lockModal.tsx |       0 |        0 |       0 |       0 | 1-360             
  ...tionModal.tsx |       0 |        0 |       0 |       0 | 1-315             
  ...icalModal.tsx |       0 |        0 |       0 |       0 | 1-116             
  ...uateModal.tsx |       0 |        0 |       0 |       0 | 1-434             
  ...zardModal.tsx |       0 |        0 |       0 |       0 | 1-337             
  monitorModal.tsx |       0 |        0 |       0 |       0 | 1-261             
  ...atchModal.tsx |       0 |        0 |       0 |       0 | 1-123             
  ...tionModal.tsx |       0 |        0 |       0 |       0 | 1-144             
  ...UnitModal.tsx |       0 |        0 |       0 |       0 | 1-369             
  ...mentModal.tsx |       0 |        0 |       0 |       0 | 1-654             
  ...tRefModal.tsx |       0 |        0 |       0 |       0 | 1-85              
  roleModal.tsx    |       0 |        0 |       0 |       0 | 1-151             
  ...cordModal.tsx |       0 |        0 |       0 |       0 | 1-336             
  ...AreaModal.tsx |       0 |        0 |       0 |       0 | 1-382             
  ...TankModal.tsx |       0 |        0 |       0 |       0 | 1-540             
  ...eGasModal.tsx |       0 |        0 |       0 |       0 | 1-211             
  userModal.tsx    |       0 |        0 |       0 |       0 | 1-172             
  ...AreaModal.tsx |       0 |        0 |       0 |       0 | 1-302             
  ...ouseModal.tsx |       0 |        0 |       0 |       0 | 1-450             
 ...ages/bigScreen |       0 |        0 |       0 |       0 |                   
  utils.ts         |       0 |        0 |       0 |       0 | 1-176             
 ...reen/component |       0 |        0 |       0 |       0 |                   
  ...Animation.tsx |       0 |        0 |       0 |       0 | 1-62              
 ...onent/Adaptive |       0 |        0 |       0 |       0 |                   
  Container.tsx    |       0 |        0 |       0 |       0 | 1-246             
  Div.tsx          |       0 |        0 |       0 |       0 | 1-14              
  Elem.tsx         |       0 |        0 |       0 |       0 | 1-181             
  Equal.tsx        |       0 |        0 |       0 |       0 | 1-181             
  Td.tsx           |       0 |        0 |       0 |       0 | 1-11              
  Text.tsx         |       0 |        0 |       0 |       0 | 1-11              
  Th.tsx           |       0 |        0 |       0 |       0 | 1-11              
  index.tsx        |       0 |        0 |       0 |       0 | 1-6               
 ...component/Card |       0 |    33.33 |   33.33 |       0 |                   
  Head.tsx         |       0 |      100 |     100 |       0 | 5-40              
  Line.tsx         |       0 |        0 |       0 |       0 | 1-67              
  index.tsx        |       0 |        0 |       0 |       0 | 1-155             
 ...omponent/Chart |       0 |        0 |       0 |       0 |                   
  Echart.tsx       |       0 |        0 |       0 |       0 | 1-45              
  fullChart.tsx    |       0 |        0 |       0 |       0 | 1-33              
  index.tsx        |       0 |        0 |       0 |       0 | 1-6               
 ...t/Chart/config |       0 |    33.33 |   33.33 |       0 |                   
  AiLine.ts        |       0 |        0 |       0 |       0 | 1-133             
  ...rSourcePie.ts |       0 |      100 |     100 |       0 | 2-229             
  Line.ts          |       0 |        0 |       0 |       0 | 1-118             
  Pie.ts           |       0 |        0 |       0 |       0 | 1-99              
  Pie3D.ts         |       0 |      100 |     100 |       0 | 2-247             
  index.tsx        |       0 |        0 |       0 |       0 | 1-7               
 ...ponent/Element |       0 |       25 |      25 |       0 |                   
  Date.tsx         |       0 |      100 |     100 |       0 | 5-43              
  ...ationCard.tsx |       0 |        0 |       0 |       0 | 1-162             
  ...ctionCard.tsx |       0 |        0 |       0 |       0 | 1-67              
  List.tsx         |       0 |        0 |       0 |       0 | 1-172             
  Modal.tsx        |       0 |        0 |       0 |       0 | 1-147             
  NoData.tsx       |       0 |        0 |       0 |       0 | 1-9               
  ResizableBox.tsx |       0 |        0 |       0 |       0 | 1-92              
  Select.tsx       |       0 |        0 |       0 |       0 | 1-80              
  SelectColor.tsx  |       0 |        0 |       0 |       0 | 1-30              
  Table.tsx        |       0 |        0 |       0 |       0 | 1-205             
  Tag.tsx          |       0 |        0 |       0 |       0 | 1-61              
  Time.tsx         |       0 |      100 |     100 |       0 | 5-46              
  Video.tsx        |       0 |        0 |       0 |       0 | 1-135             
  VideoList.tsx    |       0 |        0 |       0 |       0 | 1-77              
  Weather.tsx      |       0 |      100 |     100 |       0 | 5-40              
  index.tsx        |       0 |        0 |       0 |       0 |                   
 ...ponent/modelBg |       0 |       20 |      20 |       0 |                   
  ImgBg.tsx        |       0 |        0 |       0 |       0 | 1-32              
  ModelBg.tsx      |       0 |        0 |       0 |       0 | 1-753             
  content.ts       |       0 |        0 |       0 |       0 | 1-67              
  createLabel.ts   |       0 |      100 |     100 |       0 | 8-67              
  index.tsx        |       0 |        0 |       0 |       0 | 1-55              
 ...omponent/theme |       0 |        0 |       0 |       0 |                   
  ThemeControl.tsx |       0 |        0 |       0 |       0 | 1-540             
  constant.tsx     |       0 |        0 |       0 |       0 | 1-1252            
  index.tsx        |       0 |        0 |       0 |       0 | 1-174             
 ...igScreen/hooks |       0 |    14.28 |   14.28 |       0 |                   
  ...nistrator.tsx |       0 |        0 |       0 |       0 | 1-11              
  useAnimation.tsx |       0 |        0 |       0 |       0 | 1-54              
  ...Variables.tsx |       0 |      100 |     100 |       0 | 2-29              
  ...ayControl.tsx |       0 |        0 |       0 |       0 | 1-30              
  ...inRenewal.tsx |       0 |        0 |       0 |       0 | 1-33              
  useQuery.tsx     |       0 |        0 |       0 |       0 | 1-88              
  ...ystemAttr.tsx |       0 |        0 |       0 |       0 | 1-40              
 ...creen/template |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1-65              
 ...late/component |       0 |    22.22 |   22.22 |       0 |                   
  ...onitoring.tsx |       0 |        0 |       0 |       0 | 1-135             
  ...tScaleBar.tsx |       0 |        0 |       0 |       0 | 1-111             
  ControlStrip.tsx |       0 |        0 |       0 |       0 | 1-394             
  Head.tsx         |       0 |        0 |       0 |       0 | 1-40              
  MenuBar.tsx      |       0 |      100 |     100 |       0 | 5-157             
  NumCard.tsx      |       0 |        0 |       0 |       0 | 1-81              
  ...ationCard.tsx |       0 |        0 |       0 |       0 | 1-94              
  ...emControl.tsx |       0 |        0 |       0 |       0 | 1-123             
  Title.tsx        |       0 |      100 |     100 |       0 | 5-184             
 ...mplate/content |       0 |       50 |      50 |       0 |                   
  ...nspection.tsx |       0 |      100 |     100 |       0 | 5-30              
  index.tsx        |       0 |        0 |       0 |       0 | 1-121             
 ...tent/AiAnalyse |       0 |      100 |     100 |       0 |                   
  ...tatistics.tsx |       0 |      100 |     100 |       0 | 5-221             
  AlarmTrend.tsx   |       0 |      100 |     100 |       0 | 5-115             
  ...tionAlarm.tsx |       0 |      100 |     100 |       0 | 5-115             
  ...tionTrend.tsx |       0 |      100 |     100 |       0 | 5-174             
  index.tsx        |       0 |      100 |     100 |       0 | 5-22              
 ...sicInformation |       0 |      100 |     100 |       0 |                   
  Announcement.tsx |       0 |      100 |     100 |       0 | 5-174             
  Certificate.tsx  |       0 |      100 |     100 |       0 | 5-226             
  ...tatistics.tsx |       0 |      100 |     100 |       0 | 5-144             
  Introduce.tsx    |       0 |      100 |     100 |       0 | 5-132             
  ...tatistics.tsx |       0 |      100 |     100 |       0 | 5-234             
  index.tsx        |       0 |      100 |     100 |       0 | 5-24              
 ...ntionMechanism |       0 |       80 |      80 |       0 |                   
  ...ommitment.tsx |       0 |      100 |     100 |       0 | 5-269             
  ...iskObject.tsx |       0 |      100 |     100 |       0 | 5-129             
  ...nspection.tsx |       0 |      100 |     100 |       0 | 5-136             
  ...tribution.tsx |       0 |      100 |     100 |       0 | 5-218             
  index.tsx        |       0 |        0 |       0 |       0 | 1-18              
 ...elligentPatrol |       0 |      100 |     100 |       0 |                   
  ...tatistics.tsx |       0 |      100 |     100 |       0 | 5-93              
  ...tionTasks.tsx |       0 |      100 |     100 |       0 | 5-128             
  ...dAnalysis.tsx |       0 |      100 |     100 |       0 | 5-262             
  ...tatistics.tsx |       0 |      100 |     100 |       0 | 5-85              
  index.tsx        |       0 |      100 |     100 |       0 | 5-21              
 ...ent/KeyTargets |       0 |      100 |     100 |       0 |                   
  ...Detecting.tsx |       0 |      100 |     100 |       0 | 5-121             
  AlarmTrend.tsx   |       0 |      100 |     100 |       0 | 5-127             
  DangerSource.tsx |       0 |      100 |     100 |       0 | 5-111             
  ...formation.tsx |       0 |      100 |     100 |       0 | 5-305             
  index.tsx        |       0 |      100 |     100 |       0 | 5-22              
 ...sonnelLocation |       0 |      100 |     100 |       0 |                   
  ...tatistics.tsx |       0 |      100 |     100 |       0 | 5-95              
  Monitor.tsx      |       0 |      100 |     100 |       0 | 5-138             
  ...formation.tsx |       0 |      100 |     100 |       0 | 5-238             
  WorkArea.tsx     |       0 |      100 |     100 |       0 | 5-107             
  index.tsx        |       0 |      100 |     100 |       0 | 5-21              
 ...lJobManagement |       0 |      100 |     100 |       0 |                   
  ...Operation.tsx |       0 |      100 |     100 |       0 | 5-62              
  ...Situation.tsx |       0 |      100 |     100 |       0 | 5-154             
  WorkArea.tsx     |       0 |      100 |     100 |       0 | 5-120             
  index.tsx        |       0 |      100 |     100 |       0 | 5-17              
 ...template/modal |       0 |        0 |       0 |       0 |                   
  ...gerSource.tsx |       0 |        0 |       0 |       0 | 1-89              
  ...onitoring.tsx |       0 |        0 |       0 |       0 | 1-35              
  ...ationCard.tsx |       0 |        0 |       0 |       0 | 1-83              
  Certificate.tsx  |       0 |        0 |       0 |       0 | 1-63              
  ...formation.tsx |       0 |        0 |       0 |       0 | 1-54              
  ...tatistics.tsx |       0 |        0 |       0 |       0 | 1-23              
  GasMonitor.tsx   |       0 |        0 |       0 |       0 | 1-14              
  ...iskObject.tsx |       0 |        0 |       0 |       0 | 1-42              
  ...nspection.tsx |       0 |        0 |       0 |       0 | 1-95              
  JobBroadcast.tsx |       0 |        0 |       0 |       0 | 1-39              
  JobDetails.tsx   |       0 |        0 |       0 |       0 | 1-20              
  ...formation.tsx |       0 |        0 |       0 |       0 | 1-92              
  ...ationCard.tsx |       0 |        0 |       0 |       0 | 1-81              
  ...onMonitor.tsx |       0 |        0 |       0 |       0 | 1-26              
  ...nnelTrack.tsx |       0 |        0 |       0 |       0 | 1-47              
  RiskZones.tsx    |       0 |        0 |       0 |       0 | 1-231             
  ...alJobCard.tsx |       0 |        0 |       0 |       0 | 1-80              
  ...Situation.tsx |       0 |        0 |       0 |       0 | 1-34              
  ...oringList.tsx |       0 |        0 |       0 |       0 | 1-48              
  index.tsx        |       0 |        0 |       0 |       0 | 1-86              
 ...e/modal/Custom |       0 |    33.33 |   33.33 |       0 |                   
  Monitor.tsx      |       0 |      100 |     100 |       0 | 2-32              
  Sensor.tsx       |       0 |        0 |       0 |       0 | 1-450             
  index.tsx        |       0 |        0 |       0 |       0 | 1-87              
 ...porateTraining |       0 |    86.66 |   86.66 |       0 |                   
  ...icatePage.tsx |       0 |      100 |     100 |       0 | 5-31              
  configPage.tsx   |       0 |      100 |     100 |       0 | 5-23              
  coursePage.tsx   |       0 |      100 |     100 |       0 | 5-20              
  ...ewarePage.tsx |       0 |      100 |     100 |       0 | 5-24              
  index.tsx        |       0 |        0 |       0 |       0 | 1-13              
  paperPage.tsx    |       0 |      100 |     100 |       0 | 5-20              
  ...ecordPage.tsx |       0 |      100 |     100 |       0 | 5-33              
  peoplePage.tsx   |       0 |      100 |     100 |       0 | 5-31              
  ...ecordPage.tsx |       0 |      100 |     100 |       0 | 5-31              
  planPage.tsx     |       0 |      100 |     100 |       0 | 5-345             
  questionPage.tsx |       0 |      100 |     100 |       0 | 5-20              
  recordPage.tsx   |       0 |      100 |     100 |       0 | 5-31              
  resultPage.tsx   |       0 |        0 |       0 |       0 | 1-13              
  subjectPage.tsx  |       0 |      100 |     100 |       0 | 5-28              
  teacherPage.tsx  |       0 |      100 |     100 |       0 | 5-20              
 ...aining/content |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1-13              
 ...nt/certificate |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-33              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-59              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...content/config |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-171             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-53              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...content/course |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-33              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-107             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ent/courseware |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-225             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-89              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 .../content/paper |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-33              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-77              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...content/people |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-33              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-64              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...opleExamRecord |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-61              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-104             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...pleStudyRecord |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-36              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-92              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...g/content/plan |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-79              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-142             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ntent/question |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-234             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-89              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...content/record |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-36              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-107             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ontent/subject |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-246             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-60              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
  left.tsx         |       0 |        0 |       0 |       0 | 1-144             
 ...ontent/teacher |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-33              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-87              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...Training/modal |       0 |        0 |       0 |       0 |                   
  ...cateModal.tsx |       0 |        0 |       0 |       0 | 1-313             
  configModal.tsx  |       0 |        0 |       0 |       0 | 1-186             
  ...hareModal.tsx |       0 |        0 |       0 |       0 | 1-85              
  courseModal.tsx  |       0 |        0 |       0 |       0 | 1-409             
  ...wareModal.tsx |       0 |        0 |       0 |       0 | 1-328             
  index.tsx        |       0 |        0 |       0 |       0 | 1-13              
  paperModal.tsx   |       0 |        0 |       0 |       0 | 1-582             
  ...cordModal.tsx |       0 |        0 |       0 |       0 | 1-195             
  peopleModal.tsx  |       0 |        0 |       0 |       0 | 1-195             
  ...cordModal.tsx |       0 |        0 |       0 |       0 | 1-195             
  planModal.tsx    |       0 |        0 |       0 |       0 | 1-542             
  ...tionModal.tsx |       0 |        0 |       0 |       0 | 1-693             
  recordModal.tsx  |       0 |        0 |       0 |       0 | 1-195             
  subjectModal.tsx |       0 |        0 |       0 |       0 | 1-248             
  teacherModal.tsx |       0 |        0 |       0 |       0 | 1-319             
 ...es/doubleGuard |       0 |    65.71 |   65.71 |       0 |                   
  ...ationPage.tsx |       0 |        0 |       0 |       0 | 1-15              
  areaPage.tsx     |       0 |      100 |     100 |       0 | 3-29              
  ...enessPage.tsx |       0 |        0 |       0 |       0 | 1-19              
  bbCheckPage.tsx  |       0 |      100 |     100 |       0 | 3-39              
  ...kTaskPage.tsx |       0 |      100 |     100 |       0 | 5-24              
  bbRecordPage.tsx |       0 |        0 |       0 |       0 | 1-10              
  bbStatPage.tsx   |       0 |      100 |     100 |       0 | 3-11              
  bbTaskPage.tsx   |       0 |      100 |     100 |       0 | 5-16              
  ...egoryPage.tsx |       0 |      100 |     100 |       0 | 5-31              
  ccItemPage.tsx   |       0 |      100 |     100 |       0 | 5-31              
  ccPlanPage.tsx   |       0 |      100 |     100 |       0 | 5-31              
  ...kItemPage.tsx |       0 |      100 |     100 |       0 | 5-35              
  ccTaskPage.tsx   |       0 |      100 |     100 |       0 | 5-30              
  ...ecordPage.tsx |       0 |      100 |     100 |       0 | 5-14              
  ...klistPage.tsx |       0 |        0 |       0 |       0 | 1-28              
  dangerPage.tsx   |       0 |        0 |       0 |       0 | 1-14              
  ...IndexPage.tsx |       0 |        0 |       0 |       0 | 1-12              
  ...ctNewPage.tsx |       0 |      100 |     100 |       0 | 2-148             
  ...ctOldPage.tsx |       0 |      100 |     100 |       0 | 2-137             
  ...gencyPage.tsx |       0 |        0 |       0 |       0 | 1-19              
  eventPage.tsx    |       0 |      100 |     100 |       0 | 2-33              
  ...kTaskPage.tsx |       0 |      100 |     100 |       0 | 5-31              
  ...ecordPage.tsx |       0 |      100 |     100 |       0 | 5-43              
  ...onfigPage.tsx |       0 |      100 |     100 |       0 | 5-28              
  historyPage.tsx  |       0 |      100 |     100 |       0 | 3-12              
  ...ationPage.tsx |       0 |        0 |       0 |       0 | 1-19              
  ...ntivePage.tsx |       0 |      100 |     100 |       0 | 2-13              
  index.tsx        |       0 |      100 |     100 |       0 | 2-46              
  measurePage.tsx  |       0 |      100 |     100 |       0 | 4-25              
  mobilePage.tsx   |       0 |      100 |     100 |       0 | 5-16              
  objectPage.tsx   |       0 |        0 |       0 |       0 | 1-12              
  safetyPage.tsx   |       0 |        0 |       0 |       0 | 1-19              
  snapPage.tsx     |       0 |        0 |       0 |       0 | 1-25              
  taskListPage.tsx |       0 |        0 |       0 |       0 | 1-10              
  unitPage.tsx     |       0 |      100 |     100 |       0 | 5-18              
 ...eGuard/content |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1-39              
 ...ent/allocation |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-335             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-107             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...d/content/area |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-460             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-86              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...tent/awareness |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-309             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-51              
  index.tsx        |       0 |        0 |       0 |       0 | 1-3               
  preview.tsx      |       0 |        0 |       0 |       0 | 1-195             
 ...ontent/bbCheck |       0 |    33.33 |   33.33 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-303             
  filter.tsx       |       0 |      100 |     100 |       0 | 2-45              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...nt/bbCheckTask |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-34              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-135             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ntent/bbRecord |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-140             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-119             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...content/bbStat |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-212             
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
 ...content/bbTask |       0 |    33.33 |   33.33 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-300             
  filter.tsx       |       0 |      100 |     100 |       0 | 2-45              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ent/ccCategory |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-45              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-70              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...content/ccItem |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-45              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-90              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...content/ccPlan |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-45              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-108             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...content/ccTask |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-110             
  detail.tsx       |       0 |        0 |       0 |       0 | 1-41              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-132             
  index.tsx        |       0 |        0 |       0 |       0 | 1-3               
 ...ent/ccTaskItem |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-206             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-62              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...nt/checkRecord |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-128             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-142             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...tent/checkTask |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-252             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-149             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...tent/checklist |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-162             
  index.tsx        |       0 |        0 |       0 |       0 | 1-3               
  left.tsx         |       0 |        0 |       0 |       0 | 1-151             
  preview.tsx      |       0 |        0 |       0 |       0 | 1-155             
 ...content/danger |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-417             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-188             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ubleGuardIndex |       0 |        0 |       0 |       0 |                   
  announcement.tsx |       0 |        0 |       0 |       0 | 1-98              
  bbWorkStat.tsx   |       0 |        0 |       0 |       0 | 1-167             
  content.tsx      |       0 |        0 |       0 |       0 | 1-40              
  dangerInfo.tsx   |       0 |        0 |       0 |       0 | 1-324             
  ...erNumStat.tsx |       0 |        0 |       0 |       0 | 1-128             
  ...yRateStat.tsx |       0 |        0 |       0 |       0 | 1-138             
  distribution.tsx |       0 |        0 |       0 |       0 | 1-242             
  gcWorkStat.tsx   |       0 |        0 |       0 |       0 | 1-84              
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  my.tsx           |       0 |        0 |       0 |       0 | 1-94              
  taskInfo.tsx     |       0 |        0 |       0 |       0 | 1-336             
 ...tent/emergency |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-308             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-50              
  index.tsx        |       0 |        0 |       0 |       0 | 1-3               
  preview.tsx      |       0 |        0 |       0 |       0 | 1-180             
 .../content/event |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-493             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-111             
  index.tsx        |       0 |        0 |       0 |       0 | 1-3               
  left.tsx         |       0 |        0 |       0 |       0 | 1-138             
 ...nt/gcCheckTask |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-34              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-62              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...heckTaskRecord |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-117             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-62              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...t/gcTypeConfig |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-34              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-62              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ontent/history |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-130             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-66              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...identification |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-89              
  index.tsx        |       0 |        0 |       0 |       0 | 1-3               
  left.tsx         |       0 |        0 |       0 |       0 | 1-109             
  preview.tsx      |       0 |        0 |       0 |       0 | 1-89              
 ...tent/incentive |       0 |    33.33 |   33.33 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-157             
  filter.tsx       |       0 |      100 |     100 |       0 | 2-65              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ontent/measure |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-373             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-121             
  index.tsx        |       0 |        0 |       0 |       0 | 1-3               
  left.tsx         |       0 |        0 |       0 |       0 | 1-137             
 ...content/mobile |       0 |    33.33 |   33.33 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-286             
  filter.tsx       |       0 |      100 |     100 |       0 | 2-68              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...rd/content/obj |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-368             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-115             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...content/safety |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-302             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-50              
  index.tsx        |       0 |        0 |       0 |       0 | 1-3               
  preview.tsx      |       0 |        0 |       0 |       0 | 1-179             
 ...d/content/snap |       0 |    33.33 |   33.33 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-316             
  filter.tsx       |       0 |      100 |     100 |       0 | 2-90              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...d/content/unit |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-498             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-126             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ubleGuard/head |       0 |    33.33 |   33.33 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-173             
  filter.tsx       |       0 |      100 |     100 |       0 | 2-103             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ubleGuard/mock |       0 |        0 |       0 |       0 |                   
  index.ts         |       0 |        0 |       0 |       0 |                   
 ...bleGuard/modal |       0 |        0 |       0 |       0 |                   
  ...nessModal.tsx |       0 |        0 |       0 |       0 | 1-248             
  ...TaskModal.tsx |       0 |        0 |       0 |       0 | 1-188             
  bbTaskModal.tsx  |       0 |        0 |       0 |       0 | 1-239             
  ...goryModal.tsx |       0 |        0 |       0 |       0 | 1-223             
  ccItemModal.tsx  |       0 |        0 |       0 |       0 | 1-267             
  ccPlanModal.tsx  |       0 |        0 |       0 |       0 | 1-675             
  ...ItemModal.tsx |       0 |        0 |       0 |       0 | 1-263             
  ccTaskModal.tsx  |       0 |        0 |       0 |       0 | 1-271             
  ...encyModal.tsx |       0 |        0 |       0 |       0 | 1-307             
  ...tionModal.tsx |       0 |        0 |       0 |       0 | 1-693             
  eventModal.tsx   |       0 |        0 |       0 |       0 | 1-260             
  historyModal.tsx |       0 |        0 |       0 |       0 | 1-46              
  ...tiveModal.tsx |       0 |        0 |       0 |       0 | 1-152             
  index.tsx        |       0 |        0 |       0 |       0 | 1-29              
  measureModal.tsx |       0 |        0 |       0 |       0 | 1-324             
  mobileModal.tsx  |       0 |        0 |       0 |       0 | 1-215             
  objModal.tsx     |       0 |        0 |       0 |       0 | 1-286             
  rangeModal.tsx   |       0 |        0 |       0 |       0 | 1-314             
  safetyModal.tsx  |       0 |        0 |       0 |       0 | 1-270             
 ...dal/allocation |       0 |        0 |       0 |       0 |                   
  batchCreate.tsx  |       0 |        0 |       0 |       0 | 1-279             
  create.tsx       |       0 |        0 |       0 |       0 | 1-469             
  index.tsx        |       0 |        0 |       0 |       0 | 1-3               
  modal.tsx        |       0 |        0 |       0 |       0 | 1-302             
  tables.tsx       |       0 |        0 |       0 |       0 | 1-227             
 ...ard/modal/area |       0 |        0 |       0 |       0 |                   
  areaDrawer.tsx   |       0 |        0 |       0 |       0 | 1-346             
  areaModal.tsx    |       0 |        0 |       0 |       0 | 1-172             
  ...tionModal.tsx |       0 |        0 |       0 |       0 | 1-150             
  ...tionModal.tsx |       0 |        0 |       0 |       0 | 1-194             
  index.tsx        |       0 |        0 |       0 |       0 | 1-5               
  moreModal.tsx    |       0 |        0 |       0 |       0 | 1-111             
 .../modal/bbCheck |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
  modal.tsx        |       0 |        0 |       0 |       0 | 1-314             
  table.tsx        |       0 |        0 |       0 |       0 | 1-127             
 ...d/modal/danger |       0 |        0 |       0 |       0 |                   
  accept.tsx       |       0 |        0 |       0 |       0 | 1-192             
  ...teDelayed.tsx |       0 |        0 |       0 |       0 | 1-356             
  ...mediately.tsx |       0 |        0 |       0 |       0 | 1-343             
  delayed.tsx      |       0 |        0 |       0 |       0 | 1-278             
  editor.tsx       |       0 |        0 |       0 |       0 | 1-190             
  evaluate.tsx     |       0 |        0 |       0 |       0 | 1-354             
  immediately.tsx  |       0 |        0 |       0 |       0 | 1-284             
  index.tsx        |       0 |        0 |       0 |       0 | 1-8               
  rectify.tsx      |       0 |        0 |       0 |       0 | 1-207             
  requestDelay.tsx |       0 |        0 |       0 |       0 | 1-162             
 ...dal/evaluation |       0 |        0 |       0 |       0 |                   
  four.tsx         |       0 |        0 |       0 |       0 | 1-199             
  index.tsx        |       0 |        0 |       0 |       0 | 1-4               
  one.tsx          |       0 |        0 |       0 |       0 | 1-197             
  three.tsx        |       0 |        0 |       0 |       0 | 1-207             
  two.tsx          |       0 |        0 |       0 |       0 | 1-240             
 ...al/gcCheckTask |       0 |        0 |       0 |       0 |                   
  ...TaskModal.tsx |       0 |        0 |       0 |       0 | 1-355             
  ...ItemModal.tsx |       0 |        0 |       0 |       0 | 1-456             
  ...ngerModal.tsx |       0 |        0 |       0 |       0 | 1-320             
  ...atchModal.tsx |       0 |        0 |       0 |       0 | 1-280             
  ...cuteModal.tsx |       0 |        0 |       0 |       0 | 1-496             
  ...signModal.tsx |       0 |        0 |       0 |       0 | 1-282             
  ...cordModal.tsx |       0 |        0 |       0 |       0 | 1-195             
  ...nfigModal.tsx |       0 |        0 |       0 |       0 | 1-246             
  index.tsx        |       0 |        0 |       0 |       0 | 1-8               
 ...ard/modal/snap |       0 |        0 |       0 |       0 |                   
  delayedModal.tsx |       0 |        0 |       0 |       0 | 1-353             
  ...telyModal.tsx |       0 |        0 |       0 |       0 | 1-324             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ard/modal/unit |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1-3               
  unitModal.tsx    |       0 |        0 |       0 |       0 | 1-360             
  ...ListModal.tsx |       0 |        0 |       0 |       0 | 1-193             
  ...StopModal.tsx |       0 |        0 |       0 |       0 | 1-227             
 ...encyManagement |       0 |       80 |      80 |       0 |                   
  ...ationPage.tsx |       0 |      100 |     100 |       0 | 5-20              
  ...yPlanPage.tsx |       0 |      100 |     100 |       0 | 5-20              
  ...upplyPage.tsx |       0 |      100 |     100 |       0 | 5-20              
  ...yTeamPage.tsx |       0 |      100 |     100 |       0 | 5-20              
  index.tsx        |       0 |        0 |       0 |       0 | 1-4               
 ...gement/content |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1-4               
 ...utyInformation |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-7               
  filter.tsx       |       0 |        0 |       0 |       0 | 1-58              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 .../emergencyPlan |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-7               
  filter.tsx       |       0 |        0 |       0 |       0 | 1-86              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...mergencySupply |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-7               
  filter.tsx       |       0 |        0 |       0 |       0 | 1-90              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 .../emergencyTeam |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-7               
  filter.tsx       |       0 |        0 |       0 |       0 | 1-80              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...nagement/modal |       0 |        0 |       0 |       0 |                   
  ...tionModal.tsx |       0 |        0 |       0 |       0 | 1-310             
  ...PlanModal.tsx |       0 |        0 |       0 |       0 | 1-378             
  ...pplyModal.tsx |       0 |        0 |       0 |       0 | 1-332             
  ...TeamModal.tsx |       0 |        0 |       0 |       0 | 1-334             
  index.tsx        |       0 |        0 |       0 |       0 | 1-4               
 ...ergyManagement |       0 |       20 |      20 |       0 |                   
  ...Component.tsx |       0 |        0 |       0 |       0 | 1-571             
  ...Component.tsx |       0 |        0 |       0 |       0 | 1-276             
  ...Component.tsx |       0 |        0 |       0 |       0 | 1-193             
  ...Component.tsx |       0 |        0 |       0 |       0 | 1-176             
  ...iaoModule.tsx |       0 |      100 |     100 |       0 | 6-56              
 ...ntalProtection |       0 |        0 |       0 |       0 |                   
  ...iaoModule.tsx |       0 |        0 |       0 |       0 | 1-170             
 ...mentManagement |       0 |    88.88 |   88.88 |       0 |                   
  ...ctionPage.tsx |       0 |      100 |     100 |       0 | 5-31              
  ...egoryPage.tsx |       0 |      100 |     100 |       0 | 5-66              
  ...pmentPage.tsx |       0 |      100 |     100 |       0 | 5-658             
  index.tsx        |       0 |        0 |       0 |       0 | 1-8               
  ...nancePage.tsx |       0 |      100 |     100 |       0 | 5-31              
  repairPage.tsx   |       0 |      100 |     100 |       0 | 5-31              
  resumePage.tsx   |       0 |      100 |     100 |       0 | 5-31              
  scrapPage.tsx    |       0 |      100 |     100 |       0 | 5-31              
  stopPage.tsx     |       0 |      100 |     100 |       0 | 5-31              
 ...gement/content |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1-7               
 ...tent/detection |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-45              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-104             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...tent/equipment |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-237             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-129             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
  miscDetail.tsx   |       0 |        0 |       0 |       0 | 1-151             
 ...nt/maintenance |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-45              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-108             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...content/repair |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-45              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-118             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...content/resume |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-45              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-92              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 .../content/scrap |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-45              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-92              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...t/content/stop |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-45              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-92              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...nagement/modal |       0 |        0 |       0 |       0 |                   
  ...tionModal.tsx |       0 |        0 |       0 |       0 | 1-356             
  ...mentModal.tsx |       0 |        0 |       0 |       0 | 1-543             
  index.tsx        |       0 |        0 |       0 |       0 | 1-7               
  ...anceModal.tsx |       0 |        0 |       0 |       0 | 1-318             
  repairModal.tsx  |       0 |        0 |       0 |       0 | 1-367             
  resumeModal.tsx  |       0 |        0 |       0 |       0 | 1-306             
  scrapModal.tsx   |       0 |        0 |       0 |       0 | 1-306             
  stopModal.tsx    |       0 |        0 |       0 |       0 | 1-315             
 ...es/fireFighter |       0 |       20 |      20 |       0 |                   
  ...ayerImages.ts |       0 |      100 |     100 |       0 | 7-22              
  ...ologyPage.tsx |       0 |        0 |       0 |       0 | 1-126             
  ...iaoModule.tsx |       0 |        0 |       0 |       0 | 1-159             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
  km.js            |       0 |        0 |       0 |       0 | 1-91              
 ...gentInspection |       0 |    77.77 |   77.77 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1-8               
  ...IndexPage.tsx |       0 |        0 |       0 |       0 | 1-12              
  ...nPathPage.tsx |       0 |      100 |     100 |       0 | 5-28              
  ...PlacePage.tsx |       0 |      100 |     100 |       0 | 5-20              
  ...nPlanPage.tsx |       0 |      100 |     100 |       0 | 5-28              
  ...ndardPage.tsx |       0 |      100 |     100 |       0 | 5-20              
  ...nTaskPage.tsx |       0 |      100 |     100 |       0 | 5-28              
  ...ecordPage.tsx |       0 |      100 |     100 |       0 | 5-134             
  ...ecordPage.tsx |       0 |      100 |     100 |       0 | 5-88              
 ...ection/content |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1-8               
 ...nspectionIndex |       0 |        0 |       0 |       0 |                   
  bottom.tsx       |       0 |        0 |       0 |       0 | 1-159             
  content.tsx      |       0 |        0 |       0 |       0 | 1-134             
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  left.tsx         |       0 |        0 |       0 |       0 | 1-70              
  right.tsx        |       0 |        0 |       0 |       0 | 1-160             
 ...inspectionPath |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-173             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-75              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...nspectionPlace |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-184             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-92              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...inspectionPlan |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-33              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-90              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ectionStandard |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-41              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-62              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...inspectionTask |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-34              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-102             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...tionTaskRecord |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-56              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-117             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
  placeDetail.tsx  |       0 |        0 |       0 |       0 | 1-182             
  process.tsx      |       0 |        0 |       0 |       0 | 1-84              
 ...uledTaskRecord |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-57              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-84              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...spection/modal |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1-7               
  ...PathModal.tsx |       0 |        0 |       0 |       0 | 1-270             
  ...dardModal.tsx |       0 |        0 |       0 |       0 | 1-99              
  ...laceModal.tsx |       0 |        0 |       0 |       0 | 1-368             
  ...PlanModal.tsx |       0 |        0 |       0 |       0 | 1-748             
  ...dardModal.tsx |       0 |        0 |       0 |       0 | 1-260             
  ...TaskModal.tsx |       0 |        0 |       0 |       0 | 1-186             
  ...cordModal.tsx |       0 |        0 |       0 |       0 | 1-186             
  ...cordModal.tsx |       0 |        0 |       0 |       0 | 1-186             
 src/pages/layout  |       0 |    16.66 |   16.66 |       0 |                   
  AppQrCode.tsx    |       0 |        0 |       0 |       0 | 1-107             
  dashBoard.tsx    |       0 |        0 |       0 |       0 | 1-187             
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  layoutBox.tsx    |       0 |      100 |     100 |       0 | 2-500             
  resetPwd.tsx     |       0 |        0 |       0 |       0 | 1-137             
  tabNav.tsx       |       0 |        0 |       0 |       0 | 1-368             
 ...es/layout/icon |       0 |        0 |       0 |       0 |                   
  icon1.tsx        |       0 |        0 |       0 |       0 | 1-24              
  icon2.tsx        |       0 |        0 |       0 |       0 | 1-21              
  icon3.tsx        |       0 |        0 |       0 |       0 | 1-24              
  icon4.tsx        |       0 |        0 |       0 |       0 | 1-21              
  index.tsx        |       0 |        0 |       0 |       0 | 1-4               
 ...ges/layout/lib |       0 |        0 |       0 |       0 |                   
  hooks.tsx        |       0 |        0 |       0 |       0 | 1-59              
 src/pages/login   |       0 |        0 |       0 |       0 |                   
  authDingTalk.tsx |       0 |        0 |       0 |       0 | 1-52              
  authPage.tsx     |       0 |        0 |       0 |       0 | 1-98              
  index.tsx        |       0 |        0 |       0 |       0 | 1-3               
  loginPage.tsx    |       0 |        0 |       0 |       0 | 1-195             
 ...es/majorHazard |       0 |    85.71 |   85.71 |       0 |                   
  alarmPage.tsx    |       0 |      100 |     100 |       0 | 5-20              
  ...egoryPage.tsx |       0 |      100 |     100 |       0 | 5-20              
  index.tsx        |       0 |        0 |       0 |       0 | 1-7               
  ...rUnitPage.tsx |       0 |      100 |     100 |       0 | 5-20              
  ...AlarmPage.tsx |       0 |      100 |     100 |       0 | 5-22              
  ...AlarmPage.tsx |       0 |      100 |     100 |       0 | 5-22              
  sensorPage.tsx   |       0 |      100 |     100 |       0 | 5-25              
 ...Hazard/content |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1-6               
 .../content/alarm |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-150             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-105             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...isplayCategory |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-7               
  filter.tsx       |       0 |        0 |       0 |       0 | 1-61              
  index.tsx        |       0 |        0 |       0 |       0 | 1-6               
 ...nt/monitorUnit |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-7               
  filter.tsx       |       0 |        0 |       0 |       0 | 1-87              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
  sensorTable.tsx  |       0 |        0 |       0 |       0 | 1-87              
 ...personnelAlarm |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-119             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-96              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...content/sensor |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-208             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-157             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...nt/sensorAlarm |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-147             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-209             
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...orHazard/modal |       0 |        0 |       0 |       0 |                   
  alarmModal.tsx   |       0 |        0 |       0 |       0 | 1-170             
  ...goryModal.tsx |       0 |        0 |       0 |       0 | 1-248             
  index.tsx        |       0 |        0 |       0 |       0 | 1-6               
  ...UnitModal.tsx |       0 |        0 |       0 |       0 | 1-398             
  ...larmModal.tsx |       0 |        0 |       0 |       0 | 1-170             
  ...cessModal.tsx |       0 |        0 |       0 |       0 | 1-162             
  ...roveModal.tsx |       0 |        0 |       0 |       0 | 1-216             
  ...ysisModal.tsx |       0 |        0 |       0 |       0 | 1-238             
  ...larmModal.tsx |       0 |        0 |       0 |       0 | 1-336             
  sensorModal.tsx  |       0 |        0 |       0 |       0 | 1-675             
  ...CodeModal.tsx |       0 |        0 |       0 |       0 | 1-84              
 src/pages/map     |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  mapPage.tsx      |       0 |        0 |       0 |       0 | 1-67              
 ...pages/notFound |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  page.tsx         |       0 |        0 |       0 |       0 | 1-93              
 ...sonnelLocation |       0 |       50 |      50 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  urlPage.tsx      |       0 |      100 |     100 |       0 | 5-30              
 ...cation/content |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
 ...on/content/url |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-33              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-53              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...Location/modal |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  urlModal.tsx     |       0 |        0 |       0 |       0 | 1-186             
 ...es/specialWork |       0 |       60 |      60 |       0 |                   
  configPage.tsx   |       0 |      100 |     100 |       0 | 5-27              
  ...ervalPage.tsx |       0 |      100 |     100 |       0 | 5-20              
  index.tsx        |       0 |        0 |       0 |       0 | 1-3               
  ...ervalPage.tsx |       0 |      100 |     100 |       0 | 5-20              
  ...IndexPage.tsx |       0 |        0 |       0 |       0 | 1-12              
 ...alWork/content |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1-3               
 ...content/config |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-483             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-53              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...nt/gasInterval |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-14              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-58              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...bSliceInterval |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-31              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-59              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ecialWorkIndex |       0 |        0 |       0 |       0 |                   
  bottom.tsx       |       0 |        0 |       0 |       0 | 1-153             
  content.tsx      |       0 |        0 |       0 |       0 | 1-155             
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  left.tsx         |       0 |        0 |       0 |       0 | 1-190             
  right.tsx        |       0 |        0 |       0 |       0 | 1-136             
 ...cialWork/modal |       0 |        0 |       0 |       0 |                   
  configModal.tsx  |       0 |        0 |       0 |       0 | 1-186             
  ...rvalModal.tsx |       0 |        0 |       0 |       0 | 1-309             
  index.tsx        |       0 |        0 |       0 |       0 | 1-3               
  ...rvalModal.tsx |       0 |        0 |       0 |       0 | 1-310             
 src/pages/system  |       0 |       30 |      30 |       0 |                   
  ...onfigPage.tsx |       0 |      100 |     100 |       0 | 5-20              
  ...nAuthPage.tsx |       0 |        0 |       0 |       0 | 1-13              
  ...tingsPage.tsx |       0 |        0 |       0 |       0 | 1-13              
  ...tingsPage.tsx |       0 |        0 |       0 |       0 | 1-13              
  ...tingsPage.tsx |       0 |        0 |       0 |       0 | 1-13              
  ...tingsPage.tsx |       0 |        0 |       0 |       0 | 1-14              
  ...tingsPage.tsx |       0 |        0 |       0 |       0 | 1-13              
  index.tsx        |       0 |        0 |       0 |       0 | 1-3               
  ...onfigPage.tsx |       0 |      100 |     100 |       0 | 5-20              
  omConfigPage.tsx |       0 |      100 |     100 |       0 | 5-19              
 ...system/content |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1-3               
 ...larmPushConfig |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-31              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-75              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ent/hiddenAuth |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-255             
 ...ddenDGSettings |       0 |        0 |       0 |       0 |                   
  ...luginCard.tsx |       0 |        0 |       0 |       0 | 1-107             
  content.tsx      |       0 |        0 |       0 |       0 | 1-742             
 ...ipmentSettings |       0 |        0 |       0 |       0 |                   
  ...luginCard.tsx |       0 |        0 |       0 |       0 | 1-107             
  content.tsx      |       0 |        0 |       0 |       0 | 1-476             
 ...ddenSWSettings |       0 |        0 |       0 |       0 |                   
  ...luginCard.tsx |       0 |        0 |       0 |       0 | 1-136             
  content.tsx      |       0 |        0 |       0 |       0 | 1-342             
  ...onfigList.tsx |       0 |        0 |       0 |       0 | 1-429             
 ...SensorSettings |       0 |        0 |       0 |       0 |                   
  ...luginCard.tsx |       0 |        0 |       0 |       0 | 1-107             
  content.tsx      |       0 |        0 |       0 |       0 | 1-439             
 ...hiddenSettings |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-409             
 ...ticePushConfig |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-31              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-75              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ntent/omConfig |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-354             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-50              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...s/system/modal |       0 |        0 |       0 |       0 |                   
  ...nfigModal.tsx |       0 |        0 |       0 |       0 | 1-301             
  index.tsx        |       0 |        0 |       0 |       0 | 1-3               
  ...nfigModal.tsx |       0 |        0 |       0 |       0 | 1-268             
  ...nfigModal.tsx |       0 |        0 |       0 |       0 | 1-177             
 src/pages/ticket  |       0 |    15.78 |   15.78 |       0 |                   
  ...platePage.tsx |       0 |        0 |       0 |       0 | 1-9               
  ...onfigPage.tsx |       0 |        0 |       0 |       0 | 1-16              
  ...icketPage.tsx |       0 |        0 |       0 |       0 | 1-260             
  editorFrom.tsx   |       0 |      100 |     100 |       0 | 3-565             
  formConfig.tsx   |       0 |      100 |     100 |       0 | 2-197             
  ...ndardPage.tsx |       0 |        0 |       0 |       0 | 1-15              
  index.tsx        |       0 |      100 |     100 |       0 | 2-19              
  ...imentPage.tsx |       0 |        0 |       0 |       0 | 1-21              
  ...egoryPage.tsx |       0 |        0 |       0 |       0 | 1-16              
  ...platePage.tsx |       0 |        0 |       0 |       0 | 1-11              
  ...eUserPage.tsx |       0 |        0 |       0 |       0 | 1-15              
  preview.tsx      |       0 |        0 |       0 |       0 | 1-24              
  print.tsx        |       0 |        0 |       0 |       0 | 1-2015            
  ...platePage.tsx |       0 |        0 |       0 |       0 | 1-11              
  ...asurePage.tsx |       0 |        0 |       0 |       0 | 1-15              
  ...lysisPage.tsx |       0 |        0 |       0 |       0 | 1-18              
  ...osurePage.tsx |       0 |        0 |       0 |       0 | 1-15              
  ...asurePage.tsx |       0 |        0 |       0 |       0 | 1-15              
  ...tListPage.tsx |       0 |        0 |       0 |       0 | 1-51              
 ...onents/dispose |       0 |        0 |       0 |       0 |                   
  disposeForm.tsx  |       0 |        0 |       0 |       0 | 1-392             
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
 ...nts/eventCover |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1-38              
 ...nents/formItem |       0 |      100 |     100 |       0 |                   
  index.tsx        |       0 |      100 |     100 |       0 | 2-238             
 ...s/formItem/lib |       0 |        0 |       0 |       0 |                   
  ...tionPanel.tsx |       0 |        0 |       0 |       0 | 1-241             
  ...tionPanel.tsx |       0 |        0 |       0 |       0 | 1-202             
  ...tionPanel.tsx |       0 |        0 |       0 |       0 | 1-140             
  formTable.tsx    |       0 |        0 |       0 |       0 | 1-307             
 ...onents/preview |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  renderItem.tsx   |       0 |        0 |       0 |       0 | 1-717             
  renderTable.tsx  |       0 |        0 |       0 |       0 | 1-148             
 .../ticket/config |       0 |       25 |      25 |       0 |                   
  ...ormConfig.tsx |       0 |        0 |       0 |       0 | 1-164             
  ...ultValues.tsx |       0 |        0 |       0 |       0 | 1-63              
  ...seRegistry.ts |       0 |      100 |     100 |       0 | 3-602             
  index.tsx        |       0 |        0 |       0 |       0 | 1-3               
 ...ticket/content |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1-14              
 ...rocessTemplate |       0 |       25 |      25 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-252             
  ...itorModal.tsx |       0 |      100 |     100 |       0 | 2-39              
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  modal.tsx        |       0 |        0 |       0 |       0 | 1-170             
 ...ent/codeConfig |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-261             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-41              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...t/createTicket |       0 |        0 |       0 |       0 |                   
  ...ysisTable.tsx |       0 |        0 |       0 |       0 | 1-84              
  base.tsx         |       0 |        0 |       0 |       0 | 1-181             
  index.tsx        |       0 |        0 |       0 |       0 | 1-5               
  info.tsx         |       0 |        0 |       0 |       0 | 1-207             
  ...kersTable.tsx |       0 |        0 |       0 |       0 | 1-59              
  processes.tsx    |       0 |        0 |       0 |       0 | 1-132             
 ...nt/gasStandard |       0 |    33.33 |   33.33 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-261             
  filter.tsx       |       0 |      100 |     100 |       0 | 2-47              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...jobAppointment |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-383             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-105             
  index.tsx        |       0 |        0 |       0 |       0 | 1-3               
  side.tsx         |       0 |        0 |       0 |       0 | 1-65              
 ...pointment/tabs |       0 |        0 |       0 |       0 |                   
  approve.tsx      |       0 |        0 |       0 |       0 | 1-79              
  index.tsx        |       0 |        0 |       0 |       0 | 1-3               
  jobProgress.tsx  |       0 |        0 |       0 |       0 | 1-88              
  jobSlice.tsx     |       0 |        0 |       0 |       0 | 1-126             
 ...nt/jobCategory |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-245             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-48              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ent/jsTemplate |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-198             
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
 ...jsTemplateUser |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-313             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-51              
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
 ...rocessTemplate |       0 |       25 |      25 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-452             
  ...itorModal.tsx |       0 |      100 |     100 |       0 | 2-39              
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  modal.tsx        |       0 |        0 |       0 |       0 | 1-340             
 ...nt/riskMeasure |       0 |    33.33 |   33.33 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-261             
  filter.tsx       |       0 |      100 |     100 |       0 | 2-46              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...safetyAnalysis |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-299             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-78              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...fetyDisclosure |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-263             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-51              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 .../safetyMeasure |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-261             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-50              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ent/ticketList |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-480             
  filter.tsx       |       0 |        0 |       0 |       0 | 1-217             
  index.tsx        |       0 |        0 |       0 |       0 | 1-3               
  ...tionModal.tsx |       0 |        0 |       0 |       0 | 1-249             
  side.tsx         |       0 |        0 |       0 |       0 | 1-210             
 ...icketList/tabs |       0 |    14.28 |   14.28 |       0 |                   
  acceptRecord.tsx |       0 |        0 |       0 |       0 | 1-135             
  approve.tsx      |       0 |        0 |       0 |       0 | 1-175             
  gasAnalysis.tsx  |       0 |        0 |       0 |       0 | 1-141             
  index.tsx        |       0 |      100 |     100 |       0 | 2-11              
  infoRecord.tsx   |       0 |        0 |       0 |       0 | 1-209             
  inspection.tsx   |       0 |        0 |       0 |       0 | 1-106             
  ...tificates.tsx |       0 |        0 |       0 |       0 |                   
  jobProgress.tsx  |       0 |        0 |       0 |       0 | 1-87              
  jobSlice.tsx     |       0 |        0 |       0 |       0 | 1-98              
  onsiteRecord.tsx |       0 |        0 |       0 |       0 | 1-172             
  ...TableInfo.tsx |       0 |        0 |       0 |       0 | 1-312             
  ...tyMeasure.tsx |       0 |        0 |       0 |       0 | 1-161             
  ...sureModal.tsx |       0 |        0 |       0 |       0 | 1-176             
  safetyResult.tsx |       0 |        0 |       0 |       0 | 1-126             
 ...s/ticket/modal |       0 |        0 |       0 |       0 |                   
  ...nfigModal.tsx |       0 |        0 |       0 |       0 | 1-218             
  ...dardModal.tsx |       0 |        0 |       0 |       0 | 1-188             
  index.tsx        |       0 |        0 |       0 |       0 | 1-11              
  ...goryModal.tsx |       0 |        0 |       0 |       0 | 1-186             
  ...UserModal.tsx |       0 |        0 |       0 |       0 | 1-261             
  ...portModal.tsx |       0 |        0 |       0 |       0 | 1-125             
  ...portModal.tsx |       0 |        0 |       0 |       0 | 1-119             
  ...sureModal.tsx |       0 |        0 |       0 |       0 | 1-159             
  ...ysisModal.tsx |       0 |        0 |       0 |       0 | 1-198             
  ...sureModal.tsx |       0 |        0 |       0 |       0 | 1-185             
  ...sureModal.tsx |       0 |        0 |       0 |       0 | 1-176             
  ...ListModal.tsx |       0 |        0 |       0 |       0 | 1-112             
 ...jobAppointment |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
  ...mentModal.tsx |       0 |        0 |       0 |       0 | 1-495             
  ...mentModal.tsx |       0 |        0 |       0 |       0 | 1-135             
  useDataHooks.tsx |       0 |        0 |       0 |       0 | 1-39              
 ...s/ticket/utils |       0 |        0 |       0 |       0 |                   
  ...reVersions.ts |       0 |        0 |       0 |       0 | 1-107             
  contexts.ts      |       0 |        0 |       0 |       0 | 1-3               
  ...ltFormData.ts |       0 |        0 |       0 |       0 | 1-42              
  observers.ts     |       0 |        0 |       0 |       0 | 1-4               
 ...pages/training |       0 |       20 |      20 |       0 |                   
  ...icatePage.tsx |       0 |        0 |       0 |       0 | 1-9               
  coursePage.tsx   |       0 |        0 |       0 |       0 | 1-30              
  ...ecordPage.tsx |       0 |        0 |       0 |       0 | 1-11              
  index.tsx        |       0 |        0 |       0 |       0 | 1-4               
  myPage.tsx       |       0 |      100 |     100 |       0 | 2-10              
  recordPage.tsx   |       0 |      100 |     100 |       0 | 2-10              
  ...ecordPage.tsx |       0 |        0 |       0 |       0 | 1-10              
  ...PaperPage.tsx |       0 |        0 |       0 |       0 | 1-104             
  ...IndexPage.tsx |       0 |        0 |       0 |       0 | 1-10              
  trainingPage.tsx |       0 |        0 |       0 |       0 | 1-133             
 ...ng/certificate |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-86              
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
 ...ning/component |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
  leftBar.tsx      |       0 |        0 |       0 |       0 | 1-83              
  leftMenu.tsx     |       0 |        0 |       0 |       0 | 1-130             
 ...raining/course |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-236             
  index.tsx        |       0 |        0 |       0 |       0 | 1-5               
  interceptor.tsx  |       0 |        0 |       0 |       0 | 1-535             
  modal.tsx        |       0 |        0 |       0 |       0 | 1-181             
  paperModal.tsx   |       0 |        0 |       0 |       0 | 1-368             
  pdfModal.tsx     |       0 |        0 |       0 |       0 | 1-71              
  ...ountHooks.tsx |       0 |        0 |       0 |       0 | 1-91              
  verify.tsx       |       0 |        0 |       0 |       0 | 1-149             
 ...ing/examRecord |       0 |        0 |       0 |       0 |                   
  ...ResultPDF.tsx |       0 |        0 |       0 |       0 | 1-203             
  content.tsx      |       0 |        0 |       0 |       0 | 1-68              
  detail.tsx       |       0 |        0 |       0 |       0 | 1-40              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-101             
  index.tsx        |       0 |        0 |       0 |       0 | 1-4               
  ...ltContent.tsx |       0 |        0 |       0 |       0 | 1-392             
 ...es/training/my |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-165             
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
 ...raining/record |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-118             
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
 ...ng/studyRecord |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-42              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-91              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 .../trainingIndex |       0 |        0 |       0 |       0 |                   
  bottom.tsx       |       0 |        0 |       0 |       0 | 1-252             
  content.tsx      |       0 |        0 |       0 |       0 | 1-132             
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  left.tsx         |       0 |        0 |       0 |       0 | 1-88              
  paperStat.tsx    |       0 |        0 |       0 |       0 | 1-62              
  right.tsx        |       0 |        0 |       0 |       0 | 1-106             
 src/pages/video   |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  videoPage.tsx    |       0 |        0 |       0 |       0 | 1-20              
 ...isionDashboard |       0 |       50 |      50 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  stylePage.tsx    |       0 |      100 |     100 |       0 | 5-31              
 ...hboard/content |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
 .../content/style |       0 |        0 |       0 |       0 |                   
  content.tsx      |       0 |        0 |       0 |       0 | 1-33              
  filter.tsx       |       0 |        0 |       0 |       0 | 1-62              
  index.tsx        |       0 |        0 |       0 |       0 | 1-2               
 ...ashboard/modal |       0 |        0 |       0 |       0 |                   
  index.tsx        |       0 |        0 |       0 |       0 | 1                 
  styleModal.tsx   |       0 |        0 |       0 |       0 | 1-195             
 src/routes        |       0 |       40 |      40 |       0 |                   
  alarm.tsx        |       0 |        0 |       0 |       0 | 1-230             
  baseSetting.tsx  |       0 |        0 |       0 |       0 | 1-977             
  contractor.tsx   |       0 |        0 |       0 |       0 | 1-509             
  doubleGuard.tsx  |       0 |        0 |       0 |       0 | 1-705             
  emergency.tsx    |       0 |      100 |     100 |       0 | 4-110             
  equipment.tsx    |       0 |        0 |       0 |       0 | 1-315             
  index.ts         |       0 |      100 |     100 |       0 | 4-115             
  ...nspection.tsx |       0 |        0 |       0 |       0 | 1-232             
  majorHazard.tsx  |       0 |        0 |       0 |       0 | 1-239             
  misc.tsx         |       0 |        0 |       0 |       0 | 1-133             
  specialWork.tsx  |       0 |        0 |       0 |       0 | 1-367             
  system.tsx       |       0 |      100 |     100 |       0 | 4-229             
  training.tsx     |       0 |      100 |     100 |       0 | 4-429             
  types.ts         |       0 |        0 |       0 |       0 |                   
  utils.ts         |       0 |      100 |     100 |       0 | 4-34              
 src/types         |       0 |       75 |      75 |       0 |                   
  common.ts        |       0 |        0 |       0 |       0 |                   
  doubleGuard.ts   |       0 |        0 |       0 |       0 |                   
  index.ts         |       0 |        0 |       0 |       0 | 1-4               
  sendbox.ts       |       0 |      100 |     100 |       0 | 24-48             
 src/utils         |       0 |    27.27 |   27.27 |       0 |                   
  ...EntityDraw.ts |       0 |        0 |       0 |       0 | 1-215             
  ...EntityEdit.ts |       0 |      100 |     100 |       0 | 4-350             
  constants.ts     |       0 |        0 |       0 |       0 | 1-5               
  convertor.ts     |       0 |        0 |       0 |       0 | 1-177             
  deepMerge.ts     |       0 |      100 |     100 |       0 | 3-31              
  exports.ts       |       0 |        0 |       0 |       0 | 1-430             
  formConverter.ts |       0 |        0 |       0 |       0 | 1-137             
  index.ts         |       0 |        0 |       0 |       0 | 1-399             
  methods.ts       |       0 |      100 |     100 |       0 | 6-186             
  observer.ts      |       0 |        0 |       0 |       0 | 1-21              
  ...rConstants.ts |       0 |        0 |       0 |       0 | 1-372             
-------------------|---------|----------|---------|---------|-------------------
