# 作业票动态表单回归测试集补充分层推进计划

> 相关源码文件与文档引用：
>
> - 作业票动态表单系统源码分析：[docs/作业票动态表单系统源码分析.md](../../docs/作业票动态表单系统源码分析.md)
> - 作业票动态表单系统单元测试方案：[docs/作业票动态表单系统单元测试方案.md](../../docs/作业票动态表单系统单元测试方案.md)
> - 20250721回归测试集补充日志：[docs/dev-log/20250721-作业票动态表单回归测试集补充日志.md](../../docs/dev-log/20250721-作业票动态表单回归测试集补充日志.md)

---

## 一、项目目标与背景

### 1.1 项目目标

本次开发目标是**分层补充作业票动态表单系统25个核心文件的回归测试集**，确保新功能开发前有可靠的回归测试基准。采用**目标导向、计划驱动、持续监控、不偏离**的执行策略。

### 1.2 背景分析

- **文件范围**：基于源码分析，确认26个核心文件需要回归测试覆盖
- **分层策略**：按重要性和依赖关系分为核心层（11个）和非核心层（15个）
- **执行原则**：严格遵循20250721日志中的目标导向原则，避免范围扩大

### 1.3 核心文件清单（26个）

#### 配置管理层（6个）

- `src/pages/ticket/jsTemplateUser/content.tsx` # 模板列表管理
- `src/pages/ticket/editorFrom.tsx` # 可视化编辑器
- `src/pages/ticket/formConfig.tsx` # 表单配置页面
- `src/pages/ticket/config/defaultFormConfig.tsx` # 组件注册配置
- `src/pages/ticket/config/disposeRegistry.ts` # 组件属性配置
- `src/pages/ticket/config/defaultValues.tsx` # 默认值配置

#### 渲染引擎层（2个）

- `src/pages/ticket/components/preview/renderItem.tsx` # 动态渲染核心
- `src/pages/ticket/components/preview/renderTable.tsx` # 表格渲染组件

#### 数据转换层（4个）

- `src/pages/ticket/utils/formConverter.ts` # 数据转换主函数
- `src/pages/ticket/utils/compareVersions.ts` # 版本比较
- `src/pages/ticket/utils/defaultFormData.ts` # 默认表单数据
- `src/pages/ticket/utils/contexts.ts` # 上下文工具

#### 业务集成层（6个）

- `src/pages/ticket/createTicketPage.tsx` # 页面级数据转换与集成
- `src/pages/ticket/content/createTicket/base.tsx` # 基础信息组件
- `src/pages/ticket/content/createTicket/info.tsx` # 详细信息组件
- `src/pages/ticket/content/createTicket/analysisTable.tsx` # 分析表格组件
- `src/pages/ticket/content/createTicket/jobWorkersTable.tsx` # 作业人员表格
- `src/pages/ticket/content/createTicket/processes.tsx` # 流程组件

#### 组件库层（8个）

- `src/pages/ticket/components/formItem/index.tsx` # 组件注册表
- `src/pages/ticket/components/formItem/lib/formTable.tsx` # 表单表格组件
- `src/pages/ticket/components/formItem/lib/cellActionPanel.tsx` # 单元格操作面板
- `src/pages/ticket/components/formItem/lib/childrenActionPanel.tsx` # 子项嵌套面板
- `src/pages/ticket/components/formItem/lib/colActionPanel.tsx` # 列操作面板
- `src/pages/ticket/components/dispose/index.tsx` # 配置组件
- `src/pages/ticket/components/dispose/disposeForm.tsx` # 配置表单
- `src/pages/ticket/components/eventCover/index.tsx` # 事件覆盖组件

---

## 二、分层策略与推进计划

### 2.1 分层策略

#### 核心层（13个文件）- 优先级：最高

- **包含范围**：配置管理层核心 + 渲染引擎层 + 数据转换层 + 业务集成层核心
- **重要性**：影响整个表单系统的主链路（配置→渲染→数据转换→业务集成）
- **测试重点**：核心功能、数据流转、业务逻辑
- **预期时间**：3-4天

#### 非核心层（13个文件）- 优先级：中等

- **包含范围**：配置管理层辅助 + 业务集成层辅助 + 组件库层
- **重要性**：辅助功能和底层组件，相对独立
- **测试重点**：辅助功能、组件渲染、交互逻辑、数据绑定
- **预期时间**：2天

### 2.2 总体推进计划

| 阶段   | 目标             | 文件范围     | 优先级 | 预期时间 | 验证标准         |
| ------ | ---------------- | ------------ | ------ | -------- | ---------------- |
| 阶段一 | 核心层回归测试   | 13个核心文件 | 最高   | 3-4天    | 核心链路100%通过 |
| 阶段二 | 非核心层回归测试 | 13个辅助文件 | 中等   | 2天      | 辅助功能100%通过 |
| 阶段三 | 集成测试与优化   | 26个文件整体 | 高     | 1天      | 整体回归100%通过 |

---

## 三、分阶段详细计划

### 3.1 阶段一：核心层回归测试（11个文件）

#### 目标

确保配置管理层核心、渲染引擎层、数据转换层、业务集成层核心的功能稳定，为新功能开发提供基础保障。

#### 详细计划

##### 3.1.1 配置管理层核心测试（3个文件）

**目标**：验证表单配置的核心功能，包括组件注册、属性配置
**计划**：

1. `defaultFormConfig.tsx` - 测试默认配置加载
2. `disposeRegistry.ts` - 测试组件注册机制
3. `editorFrom.tsx` - 测试可视化编辑器

**验证标准**：

- 配置加载正确
- 组件注册机制正常
- 可视化编辑器功能完整

##### 3.1.2 渲染引擎层测试（2个文件）

**目标**：验证动态渲染引擎的核心功能
**计划**：

1. `renderItem.tsx` - 测试单字段渲染
2. `renderTable.tsx` - 测试表格渲染

**验证标准**：

- 字段渲染正确
- 表格渲染正常
- 渲染逻辑准确

##### 3.1.3 数据转换层测试（4个文件）

**目标**：验证数据转换、版本比较、默认值处理功能
**计划**：

1. `formConverter.ts` - 测试表单数据转换
2. `compareVersions.ts` - 测试版本比较功能
3. `defaultFormData.ts` - 测试默认表单数据
4. `contexts.ts` - 测试上下文工具

**验证标准**：

- 数据转换准确
- 版本比较正确
- 默认值处理准确
- 上下文管理正常

##### 3.1.4 业务集成层核心测试（2个文件）

**目标**：验证业务页面的核心集成功能
**计划**：

1. `createTicketPage.tsx` - 测试作业票创建页面
2. `info.tsx` - 测试详细信息组件

**验证标准**：

- 页面渲染正常
- 业务逻辑正确
- 数据流转准确
- 组件交互正常

#### 执行策略

- **渐进式测试**：每个文件测试完成后立即验证
- **依赖关系**：按配置→渲染→数据转换→业务集成的顺序
- **回归保障**：确保修改不影响已有功能

### 3.2 阶段二：非核心层回归测试（15个文件）

#### 目标

确保配置管理层辅助、业务集成层辅助、组件库层的功能正常，支持核心层的运行需求。

#### 详细计划

##### 3.2.1 配置管理层辅助测试（3个文件）

**计划**：

1. `content.tsx` - 测试配置内容管理
2. `formConfig.tsx` - 测试表单配置页面
3. `defaultValues.tsx` - 测试默认值处理

**验证标准**：

- 配置管理功能正常
- 页面交互正确
- 数据操作准确

##### 3.2.2 业务集成层辅助测试（4个文件）

**计划**：

1. `base.tsx` - 测试基础信息组件
2. `analysisTable.tsx` - 测试分析表格组件
3. `jobWorkersTable.tsx` - 测试作业人员表格
4. `processes.tsx` - 测试流程组件

**验证标准**：

- 组件渲染正确
- 业务逻辑准确
- 数据绑定正常

##### 3.2.3 组件库层测试（8个文件）

**计划**：

1. `formItem/index.tsx` - 测试组件注册表
2. `formTable.tsx` - 测试表单表格组件
3. `cellActionPanel.tsx` - 测试单元格操作面板
4. `childrenActionPanel.tsx` - 测试子项操作面板
5. `colActionPanel.tsx` - 测试列操作面板
6. `dispose/index.tsx` - 测试配置组件
7. `dispose/disposeForm.tsx` - 测试配置表单
8. `eventCover/index.tsx` - 测试事件覆盖组件

#### 验证标准

- 组件渲染正确
- 交互逻辑准确
- 数据绑定正常
- 样式显示正确

### 3.3 阶段三：集成测试与优化（26个文件整体）

#### 目标

确保各层协同工作，整体回归测试通过，为新功能开发提供可靠基准。

#### 详细计划

##### 3.3.1 端到端集成测试

**计划**：

1. 完整表单创建流程测试
2. 表单编辑流程测试
3. 表单预览流程测试
4. 数据提交流程测试

##### 3.3.2 性能与稳定性测试

**计划**：

1. 大量数据渲染测试
2. 复杂表单配置测试
3. 并发操作测试
4. 错误处理测试

##### 3.3.3 回归测试基准建立

**计划**：

1. 测试用例文档化
2. 自动化测试脚本
3. 覆盖率报告
4. 性能基准建立

#### 验证标准

- 端到端流程100%通过
- 性能指标达标
- 错误处理完善
- 测试覆盖充分

---

## 四、执行监控与质量保障

### 4.1 执行监控机制

#### 每日进度跟踪

- **目标检查**：每日验证阶段目标是否达成
- **问题记录**：及时记录和解决测试过程中的问题
- **进度汇报**：定期汇报测试进度和发现的问题

#### 质量检查点

- **代码质量**：确保测试代码符合项目规范
- **测试质量**：确保测试用例覆盖充分
- **文档质量**：确保测试文档完整准确

### 4.2 风险控制

#### 技术风险

- **依赖风险**：关注各层之间的依赖关系
- **兼容性风险**：确保测试不影响现有功能
- **性能风险**：监控测试对系统性能的影响

#### 进度风险

- **时间风险**：严格控制各阶段时间
- **范围风险**：避免测试范围扩大
- **质量风险**：确保测试质量不降低

---

## 五、成功标准与验收条件

### 5.1 成功标准

#### 功能标准

- 核心层13个文件测试覆盖率 ≥ 90%
- 非核心层13个文件测试覆盖率 ≥ 85%
- 整体回归测试通过率 = 100%

#### 质量标准

- 测试用例设计合理，覆盖主要场景
- 测试代码规范，符合项目标准
- 测试文档完整，便于维护

#### 性能标准

- 测试执行时间在可接受范围内
- 测试对系统性能影响最小
- 自动化程度达到预期

### 5.2 验收条件

#### 技术验收

- 所有测试用例通过
- 测试覆盖率达标
- 测试代码审查通过

#### 文档验收

- 测试计划文档完整
- 测试用例文档详细
- 测试报告准确

#### 流程验收

- 测试流程规范
- 问题处理及时
- 沟通协作顺畅

---

## 六、后续规划与建议

### 6.1 持续改进

#### 测试体系完善

- 建立自动化测试框架
- 完善测试用例库
- 优化测试执行流程

#### 质量保障提升

- 建立代码质量门禁
- 完善回归测试机制
- 提升测试效率

### 6.2 经验总结

#### 技术经验

- 分层测试策略的有效性
- 核心层优先的重要性
- 渐进式测试的优势

#### 管理经验

- 目标导向的执行效果
- 计划驱动的重要性
- 持续监控的必要性

---

## 七、任务时间与进度安排

| 阶段     | 开始时间       | 结束时间       | 预期耗时 | 主要内容             | 关键里程碑           |
| -------- | -------------- | -------------- | -------- | -------------------- | -------------------- |
| 阶段一   | 2025-07-27     | 2025-07-30     | 4天      | 核心层13个文件测试   | 核心链路100%通过     |
| 阶段二   | 2025-07-31     | 2025-08-01     | 2天      | 非核心层13个文件测试 | 辅助功能100%通过     |
| 阶段三   | 2025-08-02     | 2025-08-02     | 1天      | 集成测试与优化       | 整体回归100%通过     |
| **总计** | **2025-07-27** | **2025-08-02** | **7天**  | **26个文件回归测试** | **回归测试基准建立** |

---

## 八、用户 prompt 备忘录（时间序列，自动归纳版）

1. 要求制定分层次的推进计划，将文件分为核心和非核心几个层次
2. 要求仿照 @20250621-AlarmIndexContent.md 的命名格式，今天是20250727
3. 要求将计划写入文档，便于执行和监控
4. 要求根据源码分析文档更正文件清单，从31个文件修正为25个文件
5. 要求将可视化编辑器层和配置管理层合并，渲染引擎层的组件库相关文件挪到组件库层

> 注：本列表为自动归纳，覆盖了本次作业票动态表单回归测试集补充分层推进计划制定的所有关键用户指令和需求。

---

## 九、用户 prompt 明细原文（时间序列，完整收录）

1. 定一个计划吧，31个有点多，分核心和非核心几个层次，逐一推进，将这个计划写到文档里， 命名仿照 @20250621-AlarmIndexContent.md 今天是20250727
2. 你对比下 @作业票动态表单系统源码分析.md line20-line74，差距有多大？ 什么ContractorSearch怎么都进了？
3. 据此更正 @20250727-作业票动态表单回归测试集补充.md
4. 将可视化编辑器层和配置管理层合并，只是注明editorForm和formConfig是可视化编辑器
5. 渲染引擎层的组件库层相关的挪到组件库层
6. 我认为这样更合理

> 注：本列表为完整收录，覆盖了本次作业票动态表单回归测试集补充分层推进计划制定的所有用户指令原文。
